<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>AppFrameworkInfo.plist</key>
		<data>
		mnLbgBhrpRwdlXh4UKzYj73lYuA=
		</data>
		<key><EMAIL></key>
		<data>
		W0gmQxmINA6vbi5M4Y5KotdMnVA=
		</data>
		<key>AppIcon76x76@2x~ipad.png</key>
		<data>
		k2fBD/jokRFQCCj8xCDSOt3s78k=
		</data>
		<key>Assets.car</key>
		<data>
		Y4fguRt7niV2yhCECgRRW+3gVBc=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<data>
		28xWMBQ91UzszfdXY91SqhC7ecg=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<data>
		n2t8gsDpfE6XkhG31p7IQJRxTxU=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<data>
		ZVgM1+KwZcZnwhgaI0F7Bt1ba2c=
		</data>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<data>
		hMnf/VIyTGR2nRcoLS3JCfeGmDs=
		</data>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<data>
		MDrKFvFWroTb0+KEbQShBcoBvo4=
		</data>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<data>
		nFC1waP0YzYOchnqa85lPwrC73s=
		</data>
		<key>Frameworks/App.framework/App</key>
		<data>
		5il8C0c0CoMnuOUl470oREp8pe0=
		</data>
		<key>Frameworks/App.framework/Info.plist</key>
		<data>
		h5OB7aKzS5WR9SemvZAyN6FEkJs=
		</data>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<data>
		E6qCyW63Gp5n5tF2QCh6Q7QuJCk=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<data>
		ME0cAg6cl/bTZmwXEVgMugDccUI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<data>
		oG5/OGn+vw7vp/nu5DUDoZJ4nFc=
		</data>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<data>
		vKJkVIcw+LGHFnKJGwrQwCREv68=
		</data>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<data>
		duXxCSENxwpezI/bnbz/Q5php/s=
		</data>
		<key>Frameworks/App.framework/flutter_assets/NativeAssetsManifest.json</key>
		<data>
		re4p7E8rPLLsN+wzaPN/+AVpXTY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		/CUoTuPQqqdexfyOT9lpJhV+2MQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<data>
		w9WcwIYVRl07EMLuLi7qukruhIs=
		</data>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<data>
		Sn/Wlmb8yNLkg9BlUZxmjORrQw0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<data>
		Bvk+P1ykE1PGRdktwgwDyz6AOqM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		VvTF10G1gIeea4aI0DhJjCjHgXQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<data>
		SoHnDHqzNKozN7o/zhsvOoXVErY=
		</data>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<data>
		m1JBrnaEZeOfrb4hez5LY1b44L8=
		</data>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<data>
		wTPJHICwW6wxY3b87ek7ITN5kJk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<data>
		zbvYFr9dywry0lMMrHuNOOaNgkY=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<data>
		ksjIMu5IPw+Q3rw2YkAx0KjxkdM=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<data>
		V/wkSSsyYdMoexF6wPrC3KgkL4g=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<data>
		vFsZXNqjflvqKqAzsIptQaTSJho=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<data>
		sUgX1PJzkvyinL5i7nS1ro/Kd5o=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<data>
		SpNs7IhIC7xP34Ej+LQCaEZkqik=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<data>
		AqVvCbPmgWMQKrRnib05Okrjbp0=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<data>
		bkw+DmHReHDg1PPcvmSjuLZrheA=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<data>
		UqnnVWwQEYYX56eu7lt6dpR3LIc=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<data>
		VjAwScWkWWSrDeetip3K4yhuwDU=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<data>
		crQ9782ULebLQfIR+MbBkjB7d+k=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<data>
		ocQVSiAiUMYfVtZIn48LpYTJA5w=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<data>
		EARXud6pHb7ZYP8eXPDnluMqcXk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<data>
		qWHw5VIWEa0NmJ1PMhD16nlfRKk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<data>
		31prWLso2k5PfMMSbf5hGl+VE6Y=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<data>
		LDr6kSVbUfyQFAxLwCACF5S2VEA=
		</data>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<data>
		FSKj8+jea1tJz5Wmal2szkIiAGU=
		</data>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<data>
		wJV5dCKEGl+FAtDc8wJJh/fvKXs=
		</data>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<data>
		D+cqXttvC7E/uziGjFdqFabWd7A=
		</data>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<data>
		vBonAqoVJGE3y6WXKxWnl3w/QBg=
		</data>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<data>
		ipm8hg7aB3LzsfShJfpNR0QQ4hw=
		</data>
		<key>Frameworks/MTBBarcodeScanner.framework/Info.plist</key>
		<data>
		toMXGTAbEqGHE5Ntsh1lpKbTPes=
		</data>
		<key>Frameworks/MTBBarcodeScanner.framework/MTBBarcodeScanner</key>
		<data>
		EpNWoP97SFO0t0BqgjdSph0eJO4=
		</data>
		<key>Frameworks/MTBBarcodeScanner.framework/_CodeSignature/CodeResources</key>
		<data>
		xJSLWSuC+2QO1Z0O5I7UXcoXOjk=
		</data>
		<key>Frameworks/qr_code_scanner.framework/Info.plist</key>
		<data>
		Yki/KCwgL0VMEKerluzp3SL1+ps=
		</data>
		<key>Frameworks/qr_code_scanner.framework/_CodeSignature/CodeResources</key>
		<data>
		RP05rSGenjTufjsYaoGd+DTAFUc=
		</data>
		<key>Frameworks/qr_code_scanner.framework/qr_code_scanner</key>
		<data>
		jdHMYcfvtkJQXwL03giuWstUA7g=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/Info.plist</key>
		<data>
		UL/OmpGvTKEQyEKwBWahHMSnX6g=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/_CodeSignature/CodeResources</key>
		<data>
		srmeI8tnUZIRTnJd/jLuxjhYbyE=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation</key>
		<data>
		5XLPZ/oqmdEqhcrOsCijhSt31Ms=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<data>
		MEdvjr35tUk0hW8lcRA5Ak7gIFI=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6uLTlq7fgHHWA8emYDf4ImHC+AY=
		</data>
		<key>Frameworks/sqflite_darwin.framework/Info.plist</key>
		<data>
		xBPEI0496EP8DImeAcyo7FjowJ8=
		</data>
		<key>Frameworks/sqflite_darwin.framework/_CodeSignature/CodeResources</key>
		<data>
		NZOYkgHQroSIP6iQP9+rko+Q03g=
		</data>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin</key>
		<data>
		ZymLd7er67nUVuRlM0nyC506WbI=
		</data>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/Info.plist</key>
		<data>
		ZJ1jm0Op173tpD8oQzTpqgxyoxo=
		</data>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YIiJ5tHvqBeSpBm2mcfVZdaGz3E=
		</data>
		<key>Frameworks/url_launcher_ios.framework/Info.plist</key>
		<data>
		uJNIJkECgMr5Pakn9AcZz5eytCY=
		</data>
		<key>Frameworks/url_launcher_ios.framework/_CodeSignature/CodeResources</key>
		<data>
		DgR0F4fNevWm6RAPd4EgvX8IpwU=
		</data>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios</key>
		<data>
		586uPqdCfAGW8MQ5WRg7/XxUOuo=
		</data>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/Info.plist</key>
		<data>
		aliZ3WcpavY2GyKKi1+BoOKFt14=
		</data>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Info.plist</key>
		<data>
		PVm/mpIr+uN6jsNsRlrVKE9AoCY=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>Runner.debug.dylib</key>
		<data>
		0kjEsDNiZD/Stu4P2KfrwZmOYTg=
		</data>
		<key>__preview.dylib</key>
		<data>
		yXR9r7OZWir74XdB609VY4YG5HY=
		</data>
		<key>embedded.mobileprovision</key>
		<data>
		CXgobegTtSU58mA0+cM/FpOxssY=
		</data>
		<key>permission_handler_apple_privacy.bundle/Info.plist</key>
		<data>
		49p5YaoDhUGS6Gn6JAX92DvVt4Y=
		</data>
		<key>permission_handler_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		PgAJpgZlblxKbgx9eihlgflAQU8=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>AppFrameworkInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Zb9VR5aeuJMnm/RgXM3cr4LUNi9UZgxKD7xAgkid0NI=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			nTKNHUXzhjdeKHNhWbT/pTxJMOOWIBbb+YsCmk6AEhw=
			</data>
		</dict>
		<key>AppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			KaU6BpCNLefiwNS1DZZ8xhrLM/dmicIH5D8o6XJMOZY=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			GH74wBRuRHP4CYDNx8KVsAWfbULtAYrs/haw7QtWMfw=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			by6WshwXWgbEYiAy2bvh0UtjSVa3EwySkNFc1FazGdY=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HyVdXMU7Ux4/KalAao30mpWOK/lEPT4gvYN09wf31cg=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			VPNjf2cf66XxnoLsT0p/tEi7PPwPsYDwiapXH8jwU+I=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			BY/hOMO0FcCl8mCMQqjVbFeb8Q97c1G9lHscfspHFNk=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			PpvapAjR62rl6Ym4E6hkTgpKmBICxTaQXeUqcpHmmqQ=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			y90o2JQjssm+7ysnziyWCNMNbGqdLnZ595pTgURE5T8=
			</data>
		</dict>
		<key>Frameworks/App.framework/App</key>
		<dict>
			<key>hash2</key>
			<data>
			T31iYTH/ou6jJMZMlk3iEla5/SCTjR+6hP6b1zbLog0=
			</data>
		</dict>
		<key>Frameworks/App.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			SES+IrctFtb2ATzloiQHKMcw7x/vnPQ6XFUZixhhSAI=
			</data>
		</dict>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ZX534+a8Anw/mcd/2R3tQaWumeumSO/zc0z1mOFkmtM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			AK9VrT1vIYmP534P8JLRoc2lLJQbaGDpko1FyK+MCV0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Sps95+7JukayebvM0TLjL1LW1VXXndpKp/O8tOm9ZR8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zX4DZFvESy3Ue3y2JvUcTsv1Whl6t3JBYotHrBZfviE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			zmaoEbbqga7cvRJuXcm7ORyn5SATSVicQ36uAMA50XU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/NativeAssetsManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lUijHkoEgTXB2U+Rkyi/tirix7s8q5ZVfHlB2ql3dss=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			2YZbZxoJ1oPROoYwidiCXg9ho3aWzl19RIvIAjqmJFM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			PBOAqqWi5ujK3/k/uWcKrZyOam5QktQ5MqxZKes8MBY=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			hytJqsph4TwFteQ7enMA/idmwsNIFNaeKhIhhJsqNXY=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			Z8RP6Rg7AC553ef2l34piGYcmj5KPF/OloeH79vtgjw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			TGVjYgE+Oyl6guvhhPPrWfynkxkJeFjSzSLsQqn7Q3M=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			RTqty/gYjcrXxgR3QLjNukvL8gGpfV6MdaYktQJaIa8=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<dict>
			<key>hash2</key>
			<data>
			MGUW/wXWbGXEFX7MEj8Ns+KXft5tFgyJnxhQxu3Wuqo=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<dict>
			<key>hash2</key>
			<data>
			auaf7wPxiASCYD2ACy1dfbMJvmONwFvSz1BWYAQrrSw=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			o0iigVsmgwmtZfSv3X7hReDNYP5rXblslDnqq2s6UQc=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<dict>
			<key>hash2</key>
			<data>
			EXDk4t+7qCpyQkar+q9WHqY9bcK8eyohCwGVtBJhMy8=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<dict>
			<key>hash2</key>
			<data>
			0h9+vK5K+r8moTsiGBfs6+TM9Qog089afHAy3gbcwDU=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<dict>
			<key>hash2</key>
			<data>
			kg195C3vZLiOn8KeFQUy7DoVuA9VZDpqoBLVn64uGaI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZyqlHYuZbpFevVeny9Wdl0rVFgS7szIyssSiCyaaeFM=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<dict>
			<key>hash2</key>
			<data>
			U8q/0Ibt9q4O2HMsCdUwITtJdTx8Ljhlx+0aY83fH6s=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<dict>
			<key>hash2</key>
			<data>
			RAOC6nDhZdghbAzsIZgVeq6qPt+MUNTfm/vkUnhmZO4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SqzvIxqBXEJ3U9LJ32hCEXsrH2P16gumQ+gQx6Pdlf4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<dict>
			<key>hash2</key>
			<data>
			nmZjZpvFCXrygf4U9aPkNi8VcI7cL5AtA+CY5uUWIL0=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Q4SLFSghL/5EFJPyLg7PNi9J/xpkVVfzro0VQiQHtrY=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ebBVHSZcUnAbN4hRcYq3ttt6++z1Ybc8KVSYhVToD5k=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4hl+kRU4PNNKdAHvYrliObXzSjRzow9Z18oOMRZIa0o=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HqbvCHqKWTzs5GjLAwupqEIYVi9yf5CrMdMe31EOwUA=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			+PMn+5SDj2Vd6RU8CQIt/JYl3T+8Dhp7HImqAzocoNk=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JcpN4a9sv6xynlD3Ri611N5y+HoupUWp2hyrIXB/I8Y=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yEgZTlCNrK/A/QBjEwNGB6ffC+A9gorPvnNgSbYuQ7Y=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			+33F9+1+BOcx8Bl4XSagM9ATcVmw1/qzCNe1OPmnD0k=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			0VjriRpZ7AZZaP/0mMAPMJPhi6LoMB4MhXzL5j24tGs=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			n5XX54YqS1a2btkmvW1iLSplRagn0ZhHJ4tDjVcdQhI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			1dtlVSYHX7a7266RT1WFpkgXa70YMAhMCBG9cOrS61c=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			wSU3Ai74GJkae/7UGnbY1q6WL/vA5lEax2Kl0IRef3w=
			</data>
		</dict>
		<key>Frameworks/MTBBarcodeScanner.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			r58XEJ0vI6UNeD+Y1rBhgJaVgfutWAXwg+kc2i52trI=
			</data>
		</dict>
		<key>Frameworks/MTBBarcodeScanner.framework/MTBBarcodeScanner</key>
		<dict>
			<key>hash2</key>
			<data>
			bXzU3fNkYbokDBDHsj+0sImf8yUw7opSDpwVi0MdzOo=
			</data>
		</dict>
		<key>Frameworks/MTBBarcodeScanner.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			bYUTJ6sDSDB/QNdqWlg4YJ6RnLiAOsK7hhjbKVKJB7w=
			</data>
		</dict>
		<key>Frameworks/qr_code_scanner.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			yxW7bTMIklF1vXB4Yo++gC3YyvdNc5xg7j7ItqpvwZo=
			</data>
		</dict>
		<key>Frameworks/qr_code_scanner.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			5gc+aUf5nhUGbospqbjvIWc/UuKryH+vHgycSFSapuE=
			</data>
		</dict>
		<key>Frameworks/qr_code_scanner.framework/qr_code_scanner</key>
		<dict>
			<key>hash2</key>
			<data>
			rqqXXnqMYrHkqsG7U71kxlmXkid7tM+I2Q8ndrB0+P4=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			uoaDmxWDKa2VY7iBt2FvcZoGS709v+4Ui9d/7pmSI6o=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			YVkbSsLplYD4Rz6Kpu8ai2VLYDfRY6OMSQbqVQeLGL4=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation</key>
		<dict>
			<key>hash2</key>
			<data>
			h1CYejldyMZfNKs0PwAdd82HKJERcvtp8kLgTm9VNsA=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			vLVjyRTT7F+LNOReOOrMSuXZPMNAgOhrFkC3ogIst0Q=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			BWQouTi9VwGKYAGdFcPB7i+nJ/I2h1mLu9k0hIsYCxo=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			FkoqlGof0MlzIZ8r/unNZwUOuZGHGElLh3n9cduOhus=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			39023J+E175lHt6mOUTzxxSbB6M5qAktH48wvt55cQY=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin</key>
		<dict>
			<key>hash2</key>
			<data>
			maFOz3Nqc9RXBEn7eEtPELRFqQpxVUnWJ3zpr3D4mvw=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			tH/1Bzvo5foIQ0odgSNXL07+cVjjBsqPHHHsXKPWXv0=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			RyJqKWCN8gatChEOav61p3/1dawd+cdr/bLW37P6/tE=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			41eITRTzIShp2qRhYiSlrhFk96XYGEySTOC2ZSj3Yyc=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			7bGG6e6qybeH2gjgk5OdwpaL7qWVlC7Gs91ezvGi/Aw=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios</key>
		<dict>
			<key>hash2</key>
			<data>
			tgZllZ3dH7KO5wOhFZkKc5NRl6/EWCUG6r9CHqwydtY=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			fYqSJcrwaX8d9vQPLILIrnpHCbwUFRiyq1x1pRugQU8=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Runner.debug.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			RG7ceQNnAD28kdFQ6z6LcsHq5YiynUfqD9WxG0qkRWA=
			</data>
		</dict>
		<key>__preview.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			52ejoLEUNLniy5InYP1sVFai+Nm1PxzmNTE047gGWUI=
			</data>
		</dict>
		<key>embedded.mobileprovision</key>
		<dict>
			<key>hash2</key>
			<data>
			BnI47aFQm8ZzPRJ+o4iYPtyjwg4TEdZDw02HaPQm8dg=
			</data>
		</dict>
		<key>permission_handler_apple_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			v5ZQ/uJfugrv8aLGD60OqLOyE8C1BRGhG2JW2N79l0A=
			</data>
		</dict>
		<key>permission_handler_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			ETZWiZY6EZHpaiLgs59i8FuG0NJKvoBAXBpc7vCamxs=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
