import 'package:flutter/material.dart';
import '../utils/sample_data.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _urlVerification = true;
  bool _malwareDetection = true;
  bool _scanHistory = true;
  bool _notifications = true;
  bool _autoScan = false;
  bool _vibration = true;
  bool _soundEffects = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Settings')),
      body: ListView(
        children: [
          // Security Settings
          _buildSectionHeader('Security'),
          _buildSwitchTile(
            icon: Icons.security,
            title: 'URL Verification',
            subtitle: 'Check links for safety before opening',
            value: _urlVerification,
            onChanged: (value) => setState(() => _urlVerification = value),
          ),
          _buildSwitchTile(
            icon: Icons.warning_amber,
            title: 'Malware Detection',
            subtitle: 'Scan for malicious content',
            value: _malwareDetection,
            onChanged: (value) => setState(() => _malwareDetection = value),
          ),
          _buildSwitchTile(
            icon: Icons.history,
            title: 'Scan History',
            subtitle: 'Keep track of your scans',
            value: _scanHistory,
            onChanged: (value) => setState(() => _scanHistory = value),
          ),

          const Divider(),

          // Scanner Settings
          _buildSectionHeader('Scanner'),
          _buildSwitchTile(
            icon: Icons.auto_awesome,
            title: 'Auto Scan',
            subtitle: 'Automatically scan when QR code is detected',
            value: _autoScan,
            onChanged: (value) => setState(() => _autoScan = value),
          ),
          _buildSwitchTile(
            icon: Icons.vibration,
            title: 'Vibration',
            subtitle: 'Vibrate when QR code is scanned',
            value: _vibration,
            onChanged: (value) => setState(() => _vibration = value),
          ),
          _buildSwitchTile(
            icon: Icons.volume_up,
            title: 'Sound Effects',
            subtitle: 'Play sound when QR code is scanned',
            value: _soundEffects,
            onChanged: (value) => setState(() => _soundEffects = value),
          ),

          const Divider(),

          // Notifications
          _buildSectionHeader('Notifications'),
          _buildSwitchTile(
            icon: Icons.notifications,
            title: 'Push Notifications',
            subtitle: 'Receive security alerts and updates',
            value: _notifications,
            onChanged: (value) => setState(() => _notifications = value),
          ),

          const Divider(),

          // Data & Privacy
          _buildSectionHeader('Data & Privacy'),
          _buildListTile(
            icon: Icons.download,
            title: 'Export Data',
            subtitle: 'Download your scan history',
            onTap: _exportData,
          ),
          _buildListTile(
            icon: Icons.delete_forever,
            title: 'Clear All Data',
            subtitle: 'Remove all scan history and settings',
            onTap: _showClearDataDialog,
            textColor: Colors.red,
          ),

          const Divider(),

          // Development (only show in debug mode)
          if (const bool.fromEnvironment('dart.vm.product') == false) ...[
            _buildSectionHeader('Desenvolvimento'),
            _buildListTile(
              icon: Icons.data_usage,
              title: 'Adicionar Dados de Exemplo',
              subtitle: 'Adicionar escaneamentos de exemplo para teste',
              onTap: _addSampleData,
            ),
            const Divider(),
          ],

          // About
          _buildSectionHeader('Sobre'),
          _buildListTile(
            icon: Icons.info,
            title: 'App Version',
            subtitle: '1.0.0',
            onTap: null,
          ),
          _buildListTile(
            icon: Icons.privacy_tip,
            title: 'Privacy Policy',
            subtitle: 'Learn how we protect your data',
            onTap: _openPrivacyPolicy,
          ),
          _buildListTile(
            icon: Icons.description,
            title: 'Terms of Service',
            subtitle: 'Read our terms and conditions',
            onTap: _openTermsOfService,
          ),
          _buildListTile(
            icon: Icons.help,
            title: 'Help & Support',
            subtitle: 'Get help using QR Shield',
            onTap: _openSupport,
          ),
          _buildListTile(
            icon: Icons.star,
            title: 'Rate App',
            subtitle: 'Rate us on the App Store',
            onTap: _rateApp,
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Switch(value: value, onChanged: onChanged),
    );
  }

  Widget _buildListTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback? onTap,
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(icon, color: textColor),
      title: Text(
        title,
        style: textColor != null ? TextStyle(color: textColor) : null,
      ),
      subtitle: Text(subtitle),
      trailing: onTap != null ? const Icon(Icons.chevron_right) : null,
      onTap: onTap,
    );
  }

  void _exportData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Data'),
        content: const Text(
          'Your scan history will be exported as a CSV file.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement export functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Data exported successfully')),
              );
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  void _showClearDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'This will permanently delete all your scan history and reset all settings. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement clear data functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('All data cleared'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            child: const Text('Clear All', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _openPrivacyPolicy() {
    // TODO: Open privacy policy URL
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Opening Privacy Policy...')));
  }

  void _openTermsOfService() {
    // TODO: Open terms of service URL
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Opening Terms of Service...')),
    );
  }

  void _openSupport() {
    // TODO: Open support page or email
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Opening Support...')));
  }

  void _rateApp() {
    // TODO: Open app store rating page
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Opening App Store...')));
  }

  void _addSampleData() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Adicionar Dados de Exemplo'),
        content: const Text(
          'Isso adicionará alguns escaneamentos de exemplo para testar o aplicativo. Continuar?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await SampleDataHelper.addSampleData();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                        'Dados de exemplo adicionados com sucesso!',
                      ),
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Erro ao adicionar dados: $e')),
                  );
                }
              }
            },
            child: const Text('Adicionar'),
          ),
        ],
      ),
    );
  }
}
