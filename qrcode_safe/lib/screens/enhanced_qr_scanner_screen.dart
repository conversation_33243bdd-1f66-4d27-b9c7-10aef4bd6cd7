import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/services.dart';
import '../widgets/scan_result_dialog.dart';
import '../widgets/enhanced_qr_scanner_widget.dart';
import '../services/enhanced_qr_scanner.dart';

class EnhancedQRScannerScreen extends StatefulWidget {
  const EnhancedQRScannerScreen({super.key});

  @override
  State<EnhancedQRScannerScreen> createState() => _EnhancedQRScannerScreenState();
}

class _EnhancedQRScannerScreenState extends State<EnhancedQRScannerScreen> {
  
  void _handleScanComplete(EnhancedScanResult result) {
    if (!result.success || result.scanItem == null) {
      _handleScanError(result.error ?? 'Erro desconhecido no escaneamento');
      return;
    }

    final scanItem = result.scanItem!;
    final securityResult = result.securityResult;

    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => ScanResultDialog(
          scanItem: scanItem,
          securityResult: securityResult,
          onClose: () {
            Navigator.of(context).pop();
            Navigator.of(context).pop(); // Voltar para tela anterior
          },
          onAction: (action) async {
            Navigator.of(context).pop();
            
            await _handleScanAction(action, scanItem);
            
            // Voltar para tela anterior após ação
            if (mounted) {
              Navigator.of(context).pop();
            }
          },
        ),
      );
    }
  }

  Future<void> _handleScanAction(ScanAction action, scanItem) async {
    try {
      switch (action) {
        case ScanAction.openUrl:
          if (scanItem.type == 'url') {
            await _openUrl(scanItem.content);
          }
          break;
          
        case ScanAction.copy:
          await _copyToClipboard(scanItem.content);
          break;
          
        case ScanAction.share:
          await _shareContent(scanItem.content);
          break;
      }
    } catch (e) {
      _showErrorSnackBar('Erro ao executar ação: $e');
    }
  }

  Future<void> _openUrl(String url) async {
    try {
      // Adicionar protocolo se necessário
      String finalUrl = url;
      if (!url.toLowerCase().startsWith('http://') && 
          !url.toLowerCase().startsWith('https://')) {
        finalUrl = 'https://$url';
      }

      final uri = Uri.parse(finalUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        _showErrorSnackBar('Não foi possível abrir a URL');
      }
    } catch (e) {
      _showErrorSnackBar('URL inválida: $e');
    }
  }

  Future<void> _copyToClipboard(String content) async {
    try {
      await Clipboard.setData(ClipboardData(text: content));
      _showSuccessSnackBar('Conteúdo copiado para a área de transferência');
    } catch (e) {
      _showErrorSnackBar('Erro ao copiar: $e');
    }
  }

  Future<void> _shareContent(String content) async {
    // TODO: Implementar compartilhamento usando share_plus
    _showInfoSnackBar('Compartilhamento em desenvolvimento...');
  }

  void _handleScanError(String error) {
    _showErrorSnackBar(error);
  }

  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  void _showInfoSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.info, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.blue,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return EnhancedQRScannerWidget(
      onScanComplete: _handleScanComplete,
      onError: _handleScanError,
    );
  }
}
