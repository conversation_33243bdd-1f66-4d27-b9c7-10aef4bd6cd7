import 'package:flutter/material.dart';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  String _selectedFilter = 'All';
  final List<String> _filters = ['All', 'Safe', 'Blocked', 'Today', 'This Week'];

  // Mock data for demonstration
  final List<HistoryItem> _historyItems = [
    HistoryItem(
      id: '1',
      content: 'https://example.com',
      type: 'URL',
      status: ScanStatus.safe,
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
    ),
    HistoryItem(
      id: '2',
      content: 'Coffee Shop WiFi',
      type: 'WiFi',
      status: ScanStatus.safe,
      timestamp: DateTime.now().subtract(const Duration(days: 1)),
    ),
    HistoryItem(
      id: '3',
      content: 'Malicious URL blocked',
      type: 'URL',
      status: ScanStatus.blocked,
      timestamp: DateTime.now().subtract(const Duration(days: 3)),
    ),
    HistoryItem(
      id: '4',
      content: 'Contact information',
      type: 'vCard',
      status: ScanStatus.safe,
      timestamp: DateTime.now().subtract(const Duration(days: 5)),
    ),
    HistoryItem(
      id: '5',
      content: 'Product information',
      type: 'Text',
      status: ScanStatus.safe,
      timestamp: DateTime.now().subtract(const Duration(days: 7)),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final filteredItems = _getFilteredItems();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Scan History'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'clear':
                  _showClearHistoryDialog();
                  break;
                case 'export':
                  _exportHistory();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'clear',
                child: Row(
                  children: [
                    Icon(Icons.clear_all),
                    SizedBox(width: 8),
                    Text('Clear History'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('Export'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter chips
          Container(
            padding: const EdgeInsets.all(16),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _filters.map((filter) {
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(filter),
                      selected: _selectedFilter == filter,
                      onSelected: (selected) {
                        if (selected) {
                          setState(() {
                            _selectedFilter = filter;
                          });
                        }
                      },
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
          
          // History list
          Expanded(
            child: filteredItems.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: filteredItems.length,
                    itemBuilder: (context, index) {
                      final item = filteredItems[index];
                      return _buildHistoryItem(item);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryItem(HistoryItem item) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getStatusColor(item.status).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getTypeIcon(item.type),
            color: _getStatusColor(item.status),
            size: 20,
          ),
        ),
        title: Text(
          item.content,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Text(
                  item.type,
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodySmall?.color,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  width: 4,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Theme.of(context).textTheme.bodySmall?.color,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  _formatTimestamp(item.timestamp),
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodySmall?.color,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getStatusIcon(item.status),
              color: _getStatusColor(item.status),
              size: 16,
            ),
            const SizedBox(width: 8),
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert, size: 16),
              onSelected: (value) => _handleItemAction(value, item),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'copy',
                  child: Row(
                    children: [
                      Icon(Icons.copy, size: 16),
                      SizedBox(width: 8),
                      Text('Copy'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'share',
                  child: Row(
                    children: [
                      Icon(Icons.share, size: 16),
                      SizedBox(width: 8),
                      Text('Share'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, size: 16, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Delete', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        onTap: () => _showItemDetails(item),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: Theme.of(context).textTheme.bodySmall?.color,
          ),
          const SizedBox(height: 16),
          Text(
            'No scan history',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Your scanned QR codes will appear here',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  List<HistoryItem> _getFilteredItems() {
    switch (_selectedFilter) {
      case 'Safe':
        return _historyItems.where((item) => item.status == ScanStatus.safe).toList();
      case 'Blocked':
        return _historyItems.where((item) => item.status == ScanStatus.blocked).toList();
      case 'Today':
        final today = DateTime.now();
        return _historyItems.where((item) {
          return item.timestamp.day == today.day &&
                 item.timestamp.month == today.month &&
                 item.timestamp.year == today.year;
        }).toList();
      case 'This Week':
        final weekAgo = DateTime.now().subtract(const Duration(days: 7));
        return _historyItems.where((item) => item.timestamp.isAfter(weekAgo)).toList();
      default:
        return _historyItems;
    }
  }

  IconData _getTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'url':
        return Icons.link;
      case 'wifi':
        return Icons.wifi;
      case 'vcard':
        return Icons.contact_page;
      case 'text':
        return Icons.text_fields;
      default:
        return Icons.qr_code;
    }
  }

  IconData _getStatusIcon(ScanStatus status) {
    switch (status) {
      case ScanStatus.safe:
        return Icons.check_circle;
      case ScanStatus.warning:
        return Icons.warning;
      case ScanStatus.blocked:
        return Icons.block;
    }
  }

  Color _getStatusColor(ScanStatus status) {
    switch (status) {
      case ScanStatus.safe:
        return Colors.green;
      case ScanStatus.warning:
        return Colors.orange;
      case ScanStatus.blocked:
        return Colors.red;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _showSearchDialog() {
    // TODO: Implement search functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Search functionality coming soon')),
    );
  }

  void _showClearHistoryDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear History'),
        content: const Text('Are you sure you want to clear all scan history? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement clear history
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('History cleared')),
              );
            },
            child: const Text('Clear', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _exportHistory() {
    // TODO: Implement export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Exporting history...')),
    );
  }

  void _handleItemAction(String action, HistoryItem item) {
    switch (action) {
      case 'copy':
        // TODO: Implement copy to clipboard
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Copied to clipboard')),
        );
        break;
      case 'share':
        // TODO: Implement share functionality
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Sharing...')),
        );
        break;
      case 'delete':
        // TODO: Implement delete functionality
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Item deleted')),
        );
        break;
    }
  }

  void _showItemDetails(HistoryItem item) {
    // TODO: Implement item details view
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Details for: ${item.content}')),
    );
  }
}

class HistoryItem {
  final String id;
  final String content;
  final String type;
  final ScanStatus status;
  final DateTime timestamp;

  HistoryItem({
    required this.id,
    required this.content,
    required this.type,
    required this.status,
    required this.timestamp,
  });
}

enum ScanStatus {
  safe,
  warning,
  blocked,
}
