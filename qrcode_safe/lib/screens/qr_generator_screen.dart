import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';

class QRGeneratorScreen extends StatefulWidget {
  const QRGeneratorScreen({super.key});

  @override
  State<QRGeneratorScreen> createState() => _QRGeneratorScreenState();
}

class _QRGeneratorScreenState extends State<QRGeneratorScreen> {
  final TextEditingController _textController = TextEditingController();
  String _qrData = '';
  QRType _selectedType = QRType.text;

  final Map<QRType, String> _typeLabels = {
    QRType.text: 'Text',
    QRType.url: 'Website URL',
    QRType.wifi: 'WiFi Network',
    QRType.email: 'Email',
    QRType.phone: 'Phone Number',
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Generate QR Code'),
        actions: [
          if (_qrData.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: _shareQRCode,
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Type Selection
            Text(
              'QR Code Type',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              children: QRType.values.map((type) {
                return ChoiceChip(
                  label: Text(_typeLabels[type]!),
                  selected: _selectedType == type,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _selectedType = type;
                        _textController.clear();
                        _qrData = '';
                      });
                    }
                  },
                );
              }).toList(),
            ),
            const SizedBox(height: 24),

            // Input Section
            Text(
              'Content',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            _buildInputField(),
            const SizedBox(height: 24),

            // Generate Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _generateQRCode,
                icon: const Icon(Icons.qr_code),
                label: const Text('Generate QR Code'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
            const SizedBox(height: 32),

            // QR Code Display
            if (_qrData.isNotEmpty) ...[
              Center(
                child: Column(
                  children: [
                    Text(
                      'Your QR Code',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: QrImageView(
                        data: _qrData,
                        version: QrVersions.auto,
                        size: 200.0,
                        backgroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        OutlinedButton.icon(
                          onPressed: _saveQRCode,
                          icon: const Icon(Icons.download),
                          label: const Text('Save'),
                        ),
                        const SizedBox(width: 16),
                        OutlinedButton.icon(
                          onPressed: _shareQRCode,
                          icon: const Icon(Icons.share),
                          label: const Text('Share'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInputField() {
    switch (_selectedType) {
      case QRType.text:
        return TextField(
          controller: _textController,
          decoration: const InputDecoration(
            labelText: 'Enter text',
            hintText: 'Type your message here...',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        );
      case QRType.url:
        return TextField(
          controller: _textController,
          decoration: const InputDecoration(
            labelText: 'Website URL',
            hintText: 'https://example.com',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.link),
          ),
          keyboardType: TextInputType.url,
        );
      case QRType.wifi:
        return Column(
          children: [
            TextField(
              controller: _textController,
              decoration: const InputDecoration(
                labelText: 'Network Name (SSID)',
                hintText: 'MyWiFiNetwork',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.wifi),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              decoration: const InputDecoration(
                labelText: 'Password',
                hintText: 'Enter WiFi password',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.lock),
              ),
              obscureText: true,
            ),
          ],
        );
      case QRType.email:
        return TextField(
          controller: _textController,
          decoration: const InputDecoration(
            labelText: 'Email Address',
            hintText: '<EMAIL>',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.email),
          ),
          keyboardType: TextInputType.emailAddress,
        );
      case QRType.phone:
        return TextField(
          controller: _textController,
          decoration: const InputDecoration(
            labelText: 'Phone Number',
            hintText: '+1234567890',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.phone),
          ),
          keyboardType: TextInputType.phone,
        );
    }
  }

  void _generateQRCode() {
    final text = _textController.text.trim();
    if (text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter some content')),
      );
      return;
    }

    String qrData;
    switch (_selectedType) {
      case QRType.text:
        qrData = text;
        break;
      case QRType.url:
        qrData = text.startsWith('http') ? text : 'https://$text';
        break;
      case QRType.wifi:
        qrData = 'WIFI:T:WPA;S:$text;P:password;H:false;;';
        break;
      case QRType.email:
        qrData = 'mailto:$text';
        break;
      case QRType.phone:
        qrData = 'tel:$text';
        break;
    }

    setState(() {
      _qrData = qrData;
    });
  }

  void _saveQRCode() {
    // TODO: Implement save functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('QR Code saved to gallery')),
    );
  }

  void _shareQRCode() {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Sharing QR Code...')),
    );
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }
}

enum QRType {
  text,
  url,
  wifi,
  email,
  phone,
}
