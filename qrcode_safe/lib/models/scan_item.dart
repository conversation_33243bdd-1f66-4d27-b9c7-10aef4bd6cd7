class ScanItem {
  final int? id;
  final String content;
  final String type;
  final String status;
  final DateTime timestamp;
  final String? description;
  final bool isFavorite;

  ScanItem({
    this.id,
    required this.content,
    required this.type,
    required this.status,
    required this.timestamp,
    this.description,
    this.isFavorite = false,
  });

  // Converter para Map para inserir no banco
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'content': content,
      'type': type,
      'status': status,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'description': description,
      'isFavorite': isFavorite ? 1 : 0,
    };
  }

  // Criar ScanItem a partir do Map do banco
  factory ScanItem.fromMap(Map<String, dynamic> map) {
    return ScanItem(
      id: map['id'],
      content: map['content'],
      type: map['type'],
      status: map['status'],
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp']),
      description: map['description'],
      isFavorite: map['isFavorite'] == 1,
    );
  }

  // Criar cópia com modificações
  ScanItem copyWith({
    int? id,
    String? content,
    String? type,
    String? status,
    DateTime? timestamp,
    String? description,
    bool? isFavorite,
  }) {
    return ScanItem(
      id: id ?? this.id,
      content: content ?? this.content,
      type: type ?? this.type,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      description: description ?? this.description,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }

  @override
  String toString() {
    return 'ScanItem{id: $id, content: $content, type: $type, status: $status, timestamp: $timestamp}';
  }
}

// Enum para tipos de scan
enum ScanType {
  url('URL'),
  wifi('WiFi'),
  email('Email'),
  phone('Telefone'),
  sms('SMS'),
  text('Texto'),
  vcard('Contato'),
  location('Localização');

  const ScanType(this.displayName);
  final String displayName;
}

// Enum para status de segurança
enum SecurityStatus {
  safe('Seguro'),
  warning('Atenção'),
  blocked('Bloqueado'),
  unknown('Desconhecido');

  const SecurityStatus(this.displayName);
  final String displayName;
}
