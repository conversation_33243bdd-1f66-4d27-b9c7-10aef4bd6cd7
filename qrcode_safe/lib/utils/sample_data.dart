import '../services/data_service.dart';

class SampleDataHelper {
  static final DataService _dataService = DataService();

  static Future<void> addSampleData() async {
    // Verificar se já existem dados
    final existingScans = await _dataService.getAllScans();
    if (existingScans.isNotEmpty) {
      return; // Já existem dados, não adicionar amostras
    }

    // Adicionar dados de exemplo
    final sampleScans = [
      {
        'content': 'https://www.google.com',
        'type': 'url',
        'status': 'safe',
        'description': 'Site: www.google.com',
      },
      {
        'content': 'WIFI:T:WPA;S:MinhaRede;P:senha123;H:false;;',
        'type': 'wifi',
        'status': 'safe',
        'description': 'Rede WiFi',
      },
      {
        'content': 'mailto:<EMAIL>',
        'type': 'email',
        'status': 'safe',
        'description': 'Endereço de email',
      },
      {
        'content': 'tel:+5511999999999',
        'type': 'phone',
        'status': 'safe',
        'description': 'Número de telefone',
      },
      {
        'content': 'https://bit.ly/suspicious-link',
        'type': 'url',
        'status': 'warning',
        'description': 'Site: bit.ly',
      },
      {
        'content': 'https://malicious-site.com/virus',
        'type': 'url',
        'status': 'blocked',
        'description': 'Site: malicious-site.com',
      },
      {
        'content': 'Texto simples de exemplo para demonstração',
        'type': 'text',
        'status': 'safe',
        'description': 'Conteúdo de texto',
      },
    ];

    // Adicionar cada item com intervalos de tempo diferentes
    for (int i = 0; i < sampleScans.length; i++) {
      final scan = sampleScans[i];
      await _dataService.addScanItem(
        content: scan['content']!,
        type: scan['type']!,
        status: scan['status']!,
        description: scan['description'],
      );
      
      // Simular diferentes tempos de escaneamento
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }

  static Future<void> clearAllData() async {
    await _dataService.clearAllScans();
  }
}
