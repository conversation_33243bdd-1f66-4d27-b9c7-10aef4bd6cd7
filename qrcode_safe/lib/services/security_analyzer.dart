import 'dart:convert';
import 'dart:io';

class SecurityAnalyzer {
  static final SecurityAnalyzer _instance = SecurityAnalyzer._internal();
  factory SecurityAnalyzer() => _instance;
  SecurityAnalyzer._internal();

  // Lista de domínios suspeitos conhecidos (offline)
  static const List<String> _suspiciousDomains = [
    'bit.ly',
    'tinyurl.com',
    'short.link',
    't.co',
    'goo.gl',
    'ow.ly',
    'is.gd',
    'buff.ly',
    'adf.ly',
    'linktr.ee',
    'tiny.cc',
    'rb.gy',
    'cutt.ly',
    'shorturl.at',
    'v.gd',
    'x.co',
    'lnkd.in',
    'youtu.be',
  ];

  // Padrões de URLs maliciosas conhecidas
  static const List<String> _maliciousPatterns = [
    'phishing',
    'malware',
    'virus',
    'trojan',
    'scam',
    'fake',
    'fraud',
    'steal',
    'hack',
    'crack',
    'warez',
    'keygen',
    'serial',
    'patch',
    'download-now',
    'click-here',
    'urgent',
    'winner',
    'congratulations',
    'free-money',
    'bitcoin',
    'crypto-wallet',
    'login-verify',
    'account-suspended',
    'security-alert',
    'update-payment',
    'confirm-identity',
  ];

  // Extensões de arquivo perigosas
  static const List<String> _dangerousExtensions = [
    '.exe',
    '.bat',
    '.cmd',
    '.com',
    '.pif',
    '.scr',
    '.vbs',
    '.js',
    '.jar',
    '.app',
    '.deb',
    '.rpm',
    '.dmg',
    '.pkg',
    '.msi',
    '.apk',
  ];

  // TLDs suspeitos
  static const List<String> _suspiciousTlds = [
    '.tk',
    '.ml',
    '.ga',
    '.cf',
    '.click',
    '.download',
    '.loan',
    '.win',
    '.bid',
    '.racing',
    '.party',
    '.review',
    '.trade',
    '.date',
    '.faith',
    '.science',
    '.work',
  ];

  /// Analisa o conteúdo do QR code e retorna o nível de segurança
  SecurityResult analyzeContent(String content, String type) {
    try {
      switch (type.toLowerCase()) {
        case 'url':
          return _analyzeUrl(content);
        case 'wifi':
          return _analyzeWifi(content);
        case 'email':
          return _analyzeEmail(content);
        case 'phone':
          return _analyzePhone(content);
        case 'sms':
          return _analyzeSms(content);
        case 'vcard':
          return _analyzeVcard(content);
        case 'text':
          return _analyzeText(content);
        default:
          return SecurityResult(
            level: SecurityLevel.unknown,
            message: 'Tipo de conteúdo desconhecido',
            details: 'Não foi possível determinar o tipo de conteúdo',
            recommendations: ['Verifique manualmente o conteúdo antes de usar'],
          );
      }
    } catch (e) {
      return SecurityResult(
        level: SecurityLevel.danger,
        message: 'Erro na análise de segurança',
        details: 'Ocorreu um erro durante a análise: $e',
        recommendations: ['Não use este QR code', 'Reporte como suspeito'],
      );
    }
  }

  SecurityResult _analyzeUrl(String url) {
    final risks = <String>[];
    final recommendations = <String>[];
    var level = SecurityLevel.safe;

    try {
      final uri = Uri.parse(url);
      final domain = uri.host.toLowerCase();
      final path = uri.path.toLowerCase();
      final query = uri.query.toLowerCase();
      final fullUrl = url.toLowerCase();

      // Verificar protocolo
      if (uri.scheme != 'https') {
        risks.add('Conexão não criptografada (HTTP)');
        recommendations.add('Prefira sites com HTTPS');
        level = SecurityLevel.warning;
      }

      // Verificar domínios suspeitos
      for (final suspiciousDomain in _suspiciousDomains) {
        if (domain.contains(suspiciousDomain)) {
          risks.add('Domínio encurtador de URL detectado');
          recommendations.add('URLs encurtadas podem ocultar destinos maliciosos');
          level = SecurityLevel.warning;
          break;
        }
      }

      // Verificar TLDs suspeitos
      for (final tld in _suspiciousTlds) {
        if (domain.endsWith(tld)) {
          risks.add('Domínio com extensão suspeita ($tld)');
          recommendations.add('Extensões gratuitas são frequentemente usadas por golpistas');
          level = SecurityLevel.warning;
          break;
        }
      }

      // Verificar padrões maliciosos
      for (final pattern in _maliciousPatterns) {
        if (fullUrl.contains(pattern) || path.contains(pattern) || query.contains(pattern)) {
          risks.add('Padrão suspeito detectado: $pattern');
          recommendations.add('Este link pode ser malicioso');
          level = SecurityLevel.danger;
          break;
        }
      }

      // Verificar extensões perigosas
      for (final ext in _dangerousExtensions) {
        if (path.endsWith(ext)) {
          risks.add('Link para arquivo executável ($ext)');
          recommendations.add('Não baixe ou execute este arquivo');
          level = SecurityLevel.danger;
          break;
        }
      }

      // Verificar IP direto
      if (_isIpAddress(domain)) {
        risks.add('Link usa endereço IP direto');
        recommendations.add('Sites legítimos usam nomes de domínio');
        level = SecurityLevel.warning;
      }

      // Verificar subdomínios suspeitos
      if (domain.split('.').length > 3) {
        risks.add('Muitos subdomínios detectados');
        recommendations.add('Pode ser tentativa de imitação de site legítimo');
        level = SecurityLevel.warning;
      }

      // Verificar caracteres suspeitos
      if (_hasSuspiciousCharacters(fullUrl)) {
        risks.add('Caracteres suspeitos na URL');
        recommendations.add('URLs com caracteres especiais podem ser maliciosas');
        level = SecurityLevel.warning;
      }

      return SecurityResult(
        level: level,
        message: _getSecurityMessage(level),
        details: risks.isEmpty ? 'Nenhum risco identificado' : risks.join(', '),
        recommendations: recommendations.isEmpty 
            ? ['Verifique se o site é confiável antes de inserir dados pessoais']
            : recommendations,
        risks: risks,
      );

    } catch (e) {
      return SecurityResult(
        level: SecurityLevel.danger,
        message: 'URL inválida ou malformada',
        details: 'Não foi possível analisar a URL: $e',
        recommendations: ['Não acesse este link', 'Verifique se o QR code está correto'],
      );
    }
  }

  SecurityResult _analyzeWifi(String wifiData) {
    final risks = <String>[];
    final recommendations = <String>[];
    var level = SecurityLevel.safe;

    try {
      // Parse WiFi QR format: WIFI:T:WPA;S:MyNetwork;P:MyPassword;H:false;;
      final parts = wifiData.split(';');
      String? security, ssid, password;
      bool hidden = false;

      for (final part in parts) {
        if (part.startsWith('T:')) {
          security = part.substring(2);
        } else if (part.startsWith('S:')) {
          ssid = part.substring(2);
        } else if (part.startsWith('P:')) {
          password = part.substring(2);
        } else if (part.startsWith('H:')) {
          hidden = part.substring(2).toLowerCase() == 'true';
        }
      }

      // Verificar tipo de segurança
      if (security == null || security.isEmpty || security == 'nopass') {
        risks.add('Rede WiFi sem senha');
        recommendations.add('Redes abertas são inseguras');
        level = SecurityLevel.warning;
      } else if (security == 'WEP') {
        risks.add('Segurança WEP obsoleta');
        recommendations.add('WEP é facilmente quebrado, prefira WPA2/WPA3');
        level = SecurityLevel.warning;
      }

      // Verificar rede oculta
      if (hidden) {
        risks.add('Rede WiFi oculta');
        recommendations.add('Redes ocultas podem ser suspeitas');
        level = SecurityLevel.warning;
      }

      // Verificar nome da rede suspeito
      if (ssid != null) {
        final suspiciousNames = ['free', 'hack', 'test', 'admin', 'default', 'guest'];
        for (final name in suspiciousNames) {
          if (ssid.toLowerCase().contains(name)) {
            risks.add('Nome de rede suspeito');
            recommendations.add('Verifique se é uma rede legítima');
            level = SecurityLevel.warning;
            break;
          }
        }
      }

      return SecurityResult(
        level: level,
        message: _getSecurityMessage(level),
        details: risks.isEmpty ? 'Configuração WiFi parece segura' : risks.join(', '),
        recommendations: recommendations.isEmpty 
            ? ['Conecte apenas a redes confiáveis']
            : recommendations,
        risks: risks,
      );

    } catch (e) {
      return SecurityResult(
        level: SecurityLevel.danger,
        message: 'Dados WiFi inválidos',
        details: 'Não foi possível analisar os dados WiFi: $e',
        recommendations: ['Não use esta configuração WiFi'],
      );
    }
  }

  SecurityResult _analyzeEmail(String email) {
    final risks = <String>[];
    final recommendations = <String>[];
    var level = SecurityLevel.safe;

    try {
      final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
      
      if (!emailRegex.hasMatch(email.replaceFirst('mailto:', ''))) {
        risks.add('Formato de email inválido');
        level = SecurityLevel.warning;
      }

      final domain = email.split('@').last.toLowerCase();
      
      // Verificar domínios suspeitos
      for (final tld in _suspiciousTlds) {
        if (domain.endsWith(tld)) {
          risks.add('Domínio de email suspeito');
          recommendations.add('Verifique se o remetente é confiável');
          level = SecurityLevel.warning;
          break;
        }
      }

      return SecurityResult(
        level: level,
        message: _getSecurityMessage(level),
        details: risks.isEmpty ? 'Email parece válido' : risks.join(', '),
        recommendations: recommendations.isEmpty 
            ? ['Verifique a identidade do destinatário antes de enviar informações sensíveis']
            : recommendations,
        risks: risks,
      );

    } catch (e) {
      return SecurityResult(
        level: SecurityLevel.warning,
        message: 'Não foi possível validar o email',
        details: 'Erro na análise: $e',
        recommendations: ['Verifique manualmente o endereço de email'],
      );
    }
  }

  SecurityResult _analyzePhone(String phone) {
    final risks = <String>[];
    final recommendations = <String>[];
    var level = SecurityLevel.safe;

    // Verificar se é um número premium
    final premiumPrefixes = ['900', '901', '902', '903', '905'];
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
    
    for (final prefix in premiumPrefixes) {
      if (cleanPhone.contains(prefix)) {
        risks.add('Número premium detectado');
        recommendations.add('Ligações para este número podem ter custo elevado');
        level = SecurityLevel.warning;
        break;
      }
    }

    return SecurityResult(
      level: level,
      message: _getSecurityMessage(level),
      details: risks.isEmpty ? 'Número de telefone parece normal' : risks.join(', '),
      recommendations: recommendations.isEmpty 
          ? ['Verifique se conhece este número antes de ligar']
          : recommendations,
      risks: risks,
    );
  }

  SecurityResult _analyzeSms(String sms) {
    return SecurityResult(
      level: SecurityLevel.safe,
      message: 'SMS parece seguro',
      details: 'Conteúdo SMS analisado',
      recommendations: ['Verifique o conteúdo da mensagem antes de enviar'],
    );
  }

  SecurityResult _analyzeVcard(String vcard) {
    return SecurityResult(
      level: SecurityLevel.safe,
      message: 'vCard parece seguro',
      details: 'Informações de contato analisadas',
      recommendations: ['Verifique as informações antes de salvar o contato'],
    );
  }

  SecurityResult _analyzeText(String text) {
    final risks = <String>[];
    final recommendations = <String>[];
    var level = SecurityLevel.safe;

    // Verificar padrões suspeitos no texto
    for (final pattern in _maliciousPatterns) {
      if (text.toLowerCase().contains(pattern)) {
        risks.add('Conteúdo suspeito detectado');
        recommendations.add('Este texto pode conter informações maliciosas');
        level = SecurityLevel.warning;
        break;
      }
    }

    return SecurityResult(
      level: level,
      message: _getSecurityMessage(level),
      details: risks.isEmpty ? 'Texto parece seguro' : risks.join(', '),
      recommendations: recommendations.isEmpty 
          ? ['Leia o conteúdo com atenção']
          : recommendations,
      risks: risks,
    );
  }

  bool _isIpAddress(String domain) {
    final ipRegex = RegExp(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$');
    return ipRegex.hasMatch(domain);
  }

  bool _hasSuspiciousCharacters(String url) {
    // Verificar caracteres Unicode suspeitos que podem ser usados para phishing
    final suspiciousChars = RegExp(r'[а-я]|[α-ω]|[а-я]'); // Cirílico, grego
    return suspiciousChars.hasMatch(url);
  }

  String _getSecurityMessage(SecurityLevel level) {
    switch (level) {
      case SecurityLevel.safe:
        return 'Conteúdo verificado como seguro';
      case SecurityLevel.warning:
        return 'Conteúdo requer atenção';
      case SecurityLevel.danger:
        return 'Conteúdo perigoso - não recomendado';
      case SecurityLevel.unknown:
        return 'Status de segurança desconhecido';
    }
  }
}

enum SecurityLevel {
  safe,
  warning,
  danger,
  unknown,
}

class SecurityResult {
  final SecurityLevel level;
  final String message;
  final String details;
  final List<String> recommendations;
  final List<String> risks;

  SecurityResult({
    required this.level,
    required this.message,
    required this.details,
    required this.recommendations,
    this.risks = const [],
  });

  String get statusString {
    switch (level) {
      case SecurityLevel.safe:
        return 'safe';
      case SecurityLevel.warning:
        return 'warning';
      case SecurityLevel.danger:
        return 'blocked';
      case SecurityLevel.unknown:
        return 'unknown';
    }
  }
}
