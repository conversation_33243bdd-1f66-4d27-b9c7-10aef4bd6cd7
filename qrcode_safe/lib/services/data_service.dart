import '../database/database_helper.dart';
import '../models/scan_item.dart';

class DataService {
  static final DataService _instance = DataService._internal();
  final DatabaseHelper _dbHelper = DatabaseHelper();

  DataService._internal();

  factory DataService() => _instance;

  // Métodos para ScanItems
  Future<int> addScanItem({
    required String content,
    required String type,
    required String status,
    String? description,
  }) async {
    final item = ScanItem(
      content: content,
      type: type,
      status: status,
      timestamp: DateTime.now(),
      description: description,
    );
    return await _dbHelper.insertScanItem(item);
  }

  Future<List<ScanItem>> getAllScans() async {
    return await _dbHelper.getAllScanItems();
  }

  Future<List<ScanItem>> getRecentScans({int limit = 5}) async {
    final allScans = await _dbHelper.getAllScanItems();
    return allScans.take(limit).toList();
  }

  Future<List<ScanItem>> getScansByType(ScanType type) async {
    return await _dbHelper.getScanItemsByType(type.name);
  }

  Future<List<ScanItem>> getScansByStatus(SecurityStatus status) async {
    return await _dbHelper.getScanItemsByStatus(status.name);
  }

  Future<List<ScanItem>> getFavoriteScans() async {
    return await _dbHelper.getFavoriteScanItems();
  }

  Future<List<ScanItem>> searchScans(String query) async {
    return await _dbHelper.searchScanItems(query);
  }

  Future<void> toggleFavorite(ScanItem item) async {
    final updatedItem = item.copyWith(isFavorite: !item.isFavorite);
    await _dbHelper.updateScanItem(updatedItem);
  }

  Future<void> deleteScan(int id) async {
    await _dbHelper.deleteScanItem(id);
  }

  Future<void> clearAllScans() async {
    await _dbHelper.clearAllScanItems();
  }

  // Métodos para Configurações
  Future<bool> getBoolSetting(String key, {bool defaultValue = false}) async {
    final value = await _dbHelper.getSetting(key);
    if (value == null) return defaultValue;
    return value.toLowerCase() == 'true';
  }

  Future<String> getStringSetting(String key, {String defaultValue = ''}) async {
    final value = await _dbHelper.getSetting(key);
    return value ?? defaultValue;
  }

  Future<void> setBoolSetting(String key, bool value) async {
    await _dbHelper.setSetting(key, value.toString());
  }

  Future<void> setStringSetting(String key, String value) async {
    await _dbHelper.setSetting(key, value);
  }

  Future<Map<String, String>> getAllSettings() async {
    return await _dbHelper.getAllSettings();
  }

  // Configurações específicas
  Future<bool> isUrlVerificationEnabled() async {
    return await getBoolSetting('url_verification', defaultValue: true);
  }

  Future<void> setUrlVerification(bool enabled) async {
    await setBoolSetting('url_verification', enabled);
  }

  Future<bool> isMalwareDetectionEnabled() async {
    return await getBoolSetting('malware_detection', defaultValue: true);
  }

  Future<void> setMalwareDetection(bool enabled) async {
    await setBoolSetting('malware_detection', enabled);
  }

  Future<bool> isScanHistoryEnabled() async {
    return await getBoolSetting('scan_history', defaultValue: true);
  }

  Future<void> setScanHistory(bool enabled) async {
    await setBoolSetting('scan_history', enabled);
  }

  Future<bool> areNotificationsEnabled() async {
    return await getBoolSetting('notifications', defaultValue: true);
  }

  Future<void> setNotifications(bool enabled) async {
    await setBoolSetting('notifications', enabled);
  }

  Future<bool> isAutoScanEnabled() async {
    return await getBoolSetting('auto_scan', defaultValue: false);
  }

  Future<void> setAutoScan(bool enabled) async {
    await setBoolSetting('auto_scan', enabled);
  }

  Future<bool> isVibrationEnabled() async {
    return await getBoolSetting('vibration', defaultValue: true);
  }

  Future<void> setVibration(bool enabled) async {
    await setBoolSetting('vibration', enabled);
  }

  Future<bool> areSoundEffectsEnabled() async {
    return await getBoolSetting('sound_effects', defaultValue: true);
  }

  Future<void> setSoundEffects(bool enabled) async {
    await setBoolSetting('sound_effects', enabled);
  }

  Future<String> getThemeMode() async {
    return await getStringSetting('theme_mode', defaultValue: 'system');
  }

  Future<void> setThemeMode(String mode) async {
    await setStringSetting('theme_mode', mode);
  }

  // Métodos para Estatísticas
  Future<Map<String, int>> getStatistics() async {
    return await _dbHelper.getStatistics();
  }

  Future<int> getTotalScans() async {
    final stats = await getStatistics();
    return stats['total_scans'] ?? 0;
  }

  Future<int> getSafeScans() async {
    final stats = await getStatistics();
    return stats['safe_scans'] ?? 0;
  }

  Future<int> getBlockedScans() async {
    final stats = await getStatistics();
    return stats['blocked_scans'] ?? 0;
  }

  // Métodos utilitários
  Future<String> detectQRType(String content) async {
    if (content.startsWith('http://') || content.startsWith('https://')) {
      return ScanType.url.name;
    } else if (content.startsWith('WIFI:')) {
      return ScanType.wifi.name;
    } else if (content.startsWith('mailto:')) {
      return ScanType.email.name;
    } else if (content.startsWith('tel:')) {
      return ScanType.phone.name;
    } else if (content.startsWith('sms:')) {
      return ScanType.sms.name;
    } else if (content.startsWith('BEGIN:VCARD')) {
      return ScanType.vcard.name;
    } else if (content.startsWith('geo:')) {
      return ScanType.location.name;
    } else {
      return ScanType.text.name;
    }
  }

  Future<String> analyzeSecurityStatus(String content, String type) async {
    // Implementar lógica de análise de segurança
    // Por enquanto, retorna 'safe' para a maioria dos casos
    
    if (type == ScanType.url.name) {
      // Verificar URLs suspeitas
      final suspiciousDomains = ['bit.ly', 'tinyurl.com', 'short.link'];
      final lowerContent = content.toLowerCase();
      
      for (final domain in suspiciousDomains) {
        if (lowerContent.contains(domain)) {
          return SecurityStatus.warning.name;
        }
      }
      
      // Verificar protocolos seguros
      if (!content.startsWith('https://')) {
        return SecurityStatus.warning.name;
      }
    }
    
    return SecurityStatus.safe.name;
  }

  Future<String> getDisplayNameForType(String type) async {
    try {
      final scanType = ScanType.values.firstWhere((e) => e.name == type);
      return scanType.displayName;
    } catch (e) {
      return 'Desconhecido';
    }
  }

  Future<String> getDisplayNameForStatus(String status) async {
    try {
      final securityStatus = SecurityStatus.values.firstWhere((e) => e.name == status);
      return securityStatus.displayName;
    } catch (e) {
      return 'Desconhecido';
    }
  }

  // Método para processar um novo scan
  Future<ScanItem> processScan(String content) async {
    final type = await detectQRType(content);
    final status = await analyzeSecurityStatus(content, type);
    
    final id = await addScanItem(
      content: content,
      type: type,
      status: status,
      description: await _generateDescription(content, type),
    );

    return ScanItem(
      id: id,
      content: content,
      type: type,
      status: status,
      timestamp: DateTime.now(),
      description: await _generateDescription(content, type),
    );
  }

  Future<String?> _generateDescription(String content, String type) async {
    switch (type) {
      case 'url':
        try {
          final uri = Uri.parse(content);
          return 'Site: ${uri.host}';
        } catch (e) {
          return 'Link da web';
        }
      case 'wifi':
        return 'Rede WiFi';
      case 'email':
        return 'Endereço de email';
      case 'phone':
        return 'Número de telefone';
      case 'sms':
        return 'Mensagem SMS';
      case 'vcard':
        return 'Informações de contato';
      case 'location':
        return 'Localização geográfica';
      default:
        return 'Conteúdo de texto';
    }
  }
}
