import 'dart:async';
import 'package:flutter/services.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import 'security_analyzer.dart';
import 'data_service.dart';
import '../models/scan_item.dart';

class QRScannerService {
  static final QRScannerService _instance = QRScannerService._internal();
  factory QRScannerService() => _instance;
  QRScannerService._internal();

  final SecurityAnalyzer _securityAnalyzer = SecurityAnalyzer();
  final DataService _dataService = DataService();

  // Cache para evitar escaneamentos duplicados
  final Map<String, DateTime> _scanCache = {};
  static const Duration _cacheDuration = Duration(seconds: 3);

  // Debounce para evitar múltiplos escaneamentos
  Timer? _debounceTimer;
  static const Duration _debounceDuration = Duration(milliseconds: 500);

  /// Processa um QR code escaneado de forma segura e eficiente
  Future<ScanResult> processScan(String rawData) async {
    try {
      // Verificar cache para evitar duplicatas
      if (_isDuplicate(rawData)) {
        return ScanResult.duplicate();
      }

      // Limpar e validar dados
      final cleanData = _sanitizeData(rawData);
      if (cleanData.isEmpty) {
        return ScanResult.error('Dados do QR code estão vazios ou inválidos');
      }

      // Detectar tipo de conteúdo
      final contentType = await _detectContentType(cleanData);

      // Análise de segurança offline
      final securityResult = _securityAnalyzer.analyzeContent(
        cleanData,
        contentType,
      );

      // Criar item de scan
      final scanItem = ScanItem(
        content: cleanData,
        type: contentType,
        status: securityResult.statusString,
        timestamp: DateTime.now(),
        description: await _generateDescription(cleanData, contentType),
      );

      // Salvar no banco de dados (se habilitado)
      if (await _dataService.isScanHistoryEnabled()) {
        final id = await _dataService.addScanItem(
          content: cleanData,
          type: contentType,
          status: securityResult.statusString,
          description: scanItem.description,
        );

        // Atualizar com ID do banco
        final savedItem = scanItem.copyWith(id: id);

        // Adicionar ao cache
        _addToCache(rawData);

        // Feedback tátil se habilitado
        await _provideFeedback(securityResult.level);

        return ScanResult.success(savedItem, securityResult);
      } else {
        // Mesmo sem salvar, fornecer feedback
        await _provideFeedback(securityResult.level);
        return ScanResult.success(scanItem, securityResult);
      }
    } catch (e) {
      return ScanResult.error('Erro ao processar QR code: $e');
    }
  }

  /// Detecta o tipo de conteúdo do QR code
  Future<String> _detectContentType(String content) async {
    final lowerContent = content.toLowerCase().trim();

    // URLs
    if (lowerContent.startsWith('http://') ||
        lowerContent.startsWith('https://') ||
        lowerContent.startsWith('ftp://') ||
        _isLikelyUrl(content)) {
      return 'url';
    }

    // WiFi
    if (lowerContent.startsWith('wifi:')) {
      return 'wifi';
    }

    // Email
    if (lowerContent.startsWith('mailto:') || _isEmailAddress(content)) {
      return 'email';
    }

    // Telefone
    if (lowerContent.startsWith('tel:') || _isPhoneNumber(content)) {
      return 'phone';
    }

    // SMS
    if (lowerContent.startsWith('sms:') || lowerContent.startsWith('smsto:')) {
      return 'sms';
    }

    // vCard
    if (lowerContent.startsWith('begin:vcard')) {
      return 'vcard';
    }

    // Localização
    if (lowerContent.startsWith('geo:')) {
      return 'location';
    }

    // Calendário
    if (lowerContent.startsWith('begin:vevent')) {
      return 'calendar';
    }

    // Texto simples
    return 'text';
  }

  /// Limpa e sanitiza os dados do QR code
  String _sanitizeData(String rawData) {
    if (rawData.isEmpty) return '';

    // Remover caracteres de controle e espaços extras
    String cleaned = rawData.trim();
    cleaned = cleaned.replaceAll(RegExp(r'[\x00-\x1F\x7F]'), '');

    // Limitar tamanho máximo
    if (cleaned.length > 2048) {
      cleaned = cleaned.substring(0, 2048);
    }

    return cleaned;
  }

  /// Verifica se é um escaneamento duplicado recente
  bool _isDuplicate(String data) {
    final now = DateTime.now();
    final lastScan = _scanCache[data];

    if (lastScan != null) {
      final difference = now.difference(lastScan);
      return difference < _cacheDuration;
    }

    return false;
  }

  /// Adiciona ao cache de escaneamentos
  void _addToCache(String data) {
    final now = DateTime.now();
    _scanCache[data] = now;

    // Limpar cache antigo
    _scanCache.removeWhere((key, value) {
      return now.difference(value) > _cacheDuration;
    });
  }

  /// Fornece feedback tátil e sonoro baseado no nível de segurança
  Future<void> _provideFeedback(SecurityLevel level) async {
    try {
      // Vibração se habilitada
      if (await _dataService.isVibrationEnabled()) {
        switch (level) {
          case SecurityLevel.safe:
            HapticFeedback.lightImpact();
            break;
          case SecurityLevel.warning:
            HapticFeedback.mediumImpact();
            break;
          case SecurityLevel.danger:
            HapticFeedback.heavyImpact();
            await Future.delayed(const Duration(milliseconds: 100));
            HapticFeedback.heavyImpact();
            break;
          case SecurityLevel.unknown:
            HapticFeedback.selectionClick();
            break;
        }
      }

      // Som se habilitado
      if (await _dataService.areSoundEffectsEnabled()) {
        switch (level) {
          case SecurityLevel.safe:
            SystemSound.play(SystemSoundType.click);
            break;
          case SecurityLevel.warning:
            SystemSound.play(SystemSoundType.click);
            break;
          case SecurityLevel.danger:
            SystemSound.play(SystemSoundType.alert);
            break;
          case SecurityLevel.unknown:
            SystemSound.play(SystemSoundType.click);
            break;
        }
      }
    } catch (e) {
      // Ignorar erros de feedback
    }
  }

  /// Gera descrição automática baseada no tipo de conteúdo
  Future<String?> _generateDescription(String content, String type) async {
    try {
      switch (type) {
        case 'url':
          final uri = Uri.tryParse(content);
          if (uri != null && uri.host.isNotEmpty) {
            return 'Site: ${uri.host}';
          }
          return 'Link da web';

        case 'wifi':
          final ssidMatch = RegExp(r'S:([^;]+)').firstMatch(content);
          if (ssidMatch != null) {
            return 'WiFi: ${ssidMatch.group(1)}';
          }
          return 'Rede WiFi';

        case 'email':
          final email = content.replaceFirst('mailto:', '');
          return 'Email: $email';

        case 'phone':
          final phone = content.replaceFirst('tel:', '');
          return 'Telefone: $phone';

        case 'sms':
          return 'Mensagem SMS';

        case 'vcard':
          return 'Cartão de visita';

        case 'location':
          return 'Localização GPS';

        case 'calendar':
          return 'Evento de calendário';

        default:
          if (content.length > 50) {
            return 'Texto: ${content.substring(0, 47)}...';
          }
          return 'Texto: $content';
      }
    } catch (e) {
      return 'Conteúdo QR Code';
    }
  }

  /// Verifica se o conteúdo parece ser uma URL
  bool _isLikelyUrl(String content) {
    final urlPattern = RegExp(
      r'^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}',
      caseSensitive: false,
    );
    return urlPattern.hasMatch(content) && content.contains('.');
  }

  /// Verifica se o conteúdo é um endereço de email
  bool _isEmailAddress(String content) {
    final emailPattern = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailPattern.hasMatch(content);
  }

  /// Verifica se o conteúdo é um número de telefone
  bool _isPhoneNumber(String content) {
    final phonePattern = RegExp(r'^[\+]?[0-9\s\-\(\)]{10,}$');
    return phonePattern.hasMatch(content.trim());
  }

  /// Limpa o cache de escaneamentos
  void clearCache() {
    _scanCache.clear();
  }

  /// Cancela timers pendentes
  void dispose() {
    _debounceTimer?.cancel();
    _scanCache.clear();
  }
}

/// Resultado do processamento de um QR code
class ScanResult {
  final bool success;
  final ScanItem? scanItem;
  final SecurityResult? securityResult;
  final String? error;
  final bool isDuplicate;

  ScanResult._({
    required this.success,
    this.scanItem,
    this.securityResult,
    this.error,
    this.isDuplicate = false,
  });

  factory ScanResult.success(ScanItem item, SecurityResult security) {
    return ScanResult._(
      success: true,
      scanItem: item,
      securityResult: security,
    );
  }

  factory ScanResult.error(String errorMessage) {
    return ScanResult._(success: false, error: errorMessage);
  }

  factory ScanResult.duplicate() {
    return ScanResult._(
      success: false,
      isDuplicate: true,
      error: 'QR code já foi escaneado recentemente',
    );
  }
}
