import 'dart:async';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import 'security_analyzer.dart';
import 'data_service.dart';
import '../models/scan_item.dart';

class EnhancedQRScanner {
  static final EnhancedQRScanner _instance = EnhancedQRScanner._internal();
  factory EnhancedQRScanner() => _instance;
  EnhancedQRScanner._internal();

  final SecurityAnalyzer _securityAnalyzer = SecurityAnalyzer();
  final DataService _dataService = DataService();

  // Controle de estado do scanner
  QRViewController? _controller;
  bool _isScanning = false;
  bool _isProcessing = false;

  // Cache e controle de duplicatas
  final Map<String, DateTime> _scanCache = {};
  final Map<String, int> _scanAttempts = {};
  static const Duration _cacheDuration = Duration(seconds: 5);
  static const int _maxScanAttempts = 3;

  // Controle de qualidade
  Timer? _qualityTimer;
  int _consecutiveFailures = 0;
  static const int _maxConsecutiveFailures = 5;

  // Métricas de performance
  final List<Duration> _scanTimes = [];
  static const int _maxMetrics = 10;

  /// Inicializa o scanner com configurações otimizadas
  Future<void> initialize(QRViewController controller) async {
    _controller = controller;
    _isScanning = true;
    _isProcessing = false;

    // Configurações otimizadas para melhor detecção
    await _configureCamera();

    // Iniciar monitoramento de qualidade
    _startQualityMonitoring();

    // Configurar stream de dados
    _setupScanStream();
  }

  /// Configura a câmera para melhor performance
  Future<void> _configureCamera() async {
    if (_controller == null) return;

    try {
      // Configurar resolução otimizada
      await _controller!.pauseCamera();

      // Aguardar um momento para estabilizar
      await Future.delayed(const Duration(milliseconds: 500));

      // Retomar com configurações otimizadas
      await _controller!.resumeCamera();

      // Configurar foco automático se disponível
      if (Platform.isAndroid) {
        // Configurações específicas para Android
        await _controller!.flipCamera();
        await Future.delayed(const Duration(milliseconds: 200));
        await _controller!.flipCamera();
      }
    } catch (e) {
      // Ignorar erros de configuração da câmera
    }
  }

  /// Configura o stream de escaneamento com otimizações
  void _setupScanStream() {
    _controller?.scannedDataStream.listen(
      (scanData) => _handleScanData(scanData),
      onError: (error) => _handleScanError(error),
    );
  }

  /// Processa dados escaneados com validações avançadas
  Future<void> _handleScanData(Barcode scanData) async {
    if (!_isScanning || _isProcessing || scanData.code == null) {
      return;
    }

    final code = scanData.code!;
    final startTime = DateTime.now();

    // Verificar se é um escaneamento válido
    if (!_isValidScan(code)) {
      _consecutiveFailures++;
      return;
    }

    // Verificar cache de duplicatas
    if (_isDuplicateScan(code)) {
      return;
    }

    // Marcar como processando
    _isProcessing = true;
    _pauseScanning();

    try {
      // Processar o QR code
      final result = await _processQRCode(code);

      // Registrar métricas
      _recordScanTime(DateTime.now().difference(startTime));

      // Reset contador de falhas
      _consecutiveFailures = 0;

      // Adicionar ao cache
      _addToCache(code);

      // Callback com resultado
      _onScanComplete?.call(result);
    } catch (e) {
      _consecutiveFailures++;
      _onScanError?.call('Erro ao processar QR code: $e');
    } finally {
      _isProcessing = false;

      // Aguardar antes de retomar escaneamento
      await Future.delayed(const Duration(milliseconds: 1500));
      _resumeScanning();
    }
  }

  /// Valida se o scan é de qualidade suficiente
  bool _isValidScan(String code) {
    // Verificar tamanho mínimo
    if (code.length < 3) return false;

    // Verificar caracteres válidos
    if (code.trim().isEmpty) return false;

    // Verificar se não é apenas espaços ou caracteres especiais
    if (RegExp(r'^[\s\n\r\t]*$').hasMatch(code)) return false;

    // Verificar se não é um código muito repetitivo (possível erro)
    if (_isRepetitiveCode(code)) return false;

    return true;
  }

  /// Verifica se o código é muito repetitivo (possível erro de leitura)
  bool _isRepetitiveCode(String code) {
    if (code.length < 10) return false;

    // Verificar se mais de 70% dos caracteres são iguais
    final charCount = <String, int>{};
    for (final char in code.split('')) {
      charCount[char] = (charCount[char] ?? 0) + 1;
    }

    final maxCount = charCount.values.reduce((a, b) => a > b ? a : b);
    return (maxCount / code.length) > 0.7;
  }

  /// Verifica se é um escaneamento duplicado
  bool _isDuplicateScan(String code) {
    final now = DateTime.now();
    final lastScan = _scanCache[code];

    if (lastScan != null) {
      final timeDiff = now.difference(lastScan);
      if (timeDiff < _cacheDuration) {
        // Incrementar tentativas
        _scanAttempts[code] = (_scanAttempts[code] ?? 0) + 1;

        // Se muitas tentativas, pode ser um código problemático
        if ((_scanAttempts[code] ?? 0) > _maxScanAttempts) {
          _addToCache(code, extended: true); // Cache estendido
        }

        return true;
      }
    }

    return false;
  }

  /// Processa o QR code com análise de segurança
  Future<EnhancedScanResult> _processQRCode(String code) async {
    // Limpar e sanitizar dados
    final cleanCode = _sanitizeCode(code);

    // Detectar tipo de conteúdo
    final contentType = await _detectContentType(cleanCode);

    // Análise de segurança
    final securityResult = _securityAnalyzer.analyzeContent(
      cleanCode,
      contentType,
    );

    // Gerar descrição
    final description = await _generateDescription(cleanCode, contentType);

    // Criar item de scan
    final scanItem = ScanItem(
      content: cleanCode,
      type: contentType,
      status: securityResult.statusString,
      timestamp: DateTime.now(),
      description: description,
    );

    // Salvar no banco se habilitado
    int? savedId;
    if (await _dataService.isScanHistoryEnabled()) {
      savedId = await _dataService.addScanItem(
        content: cleanCode,
        type: contentType,
        status: securityResult.statusString,
        description: description,
      );
    }

    // Feedback baseado no nível de segurança
    await _provideFeedback(securityResult.level);

    return EnhancedScanResult(
      success: true,
      scanItem: scanItem.copyWith(id: savedId),
      securityResult: securityResult,
      scanQuality: _calculateScanQuality(code),
      processingTime: DateTime.now().difference(DateTime.now()),
    );
  }

  /// Calcula a qualidade do escaneamento
  ScanQuality _calculateScanQuality(String code) {
    int score = 100;

    // Penalizar códigos muito curtos
    if (code.length < 10) score -= 20;

    // Penalizar códigos com muitos caracteres especiais
    final specialChars = RegExp(r'[^\w\s\-\.\:\/\@]').allMatches(code).length;
    if (specialChars > code.length * 0.3) score -= 15;

    // Bonificar códigos com estrutura reconhecível
    if (_hasRecognizableStructure(code)) score += 10;

    // Considerar histórico de falhas
    if (_consecutiveFailures > 0) score -= (_consecutiveFailures * 5);

    score = score.clamp(0, 100);

    if (score >= 80) return ScanQuality.excellent;
    if (score >= 60) return ScanQuality.good;
    if (score >= 40) return ScanQuality.fair;
    return ScanQuality.poor;
  }

  /// Verifica se o código tem estrutura reconhecível
  bool _hasRecognizableStructure(String code) {
    final lowerCode = code.toLowerCase();

    // URLs
    if (lowerCode.startsWith('http') ||
        lowerCode.contains('.com') ||
        lowerCode.contains('.br')) {
      return true;
    }

    // WiFi
    if (lowerCode.startsWith('wifi:')) return true;

    // Email
    if (lowerCode.contains('@') && lowerCode.contains('.')) return true;

    // Telefone
    if (RegExp(r'^\+?[\d\s\-\(\)]{10,}$').hasMatch(code)) return true;

    return false;
  }

  /// Sanitiza o código escaneado
  String _sanitizeCode(String code) {
    // Remover caracteres de controle
    String cleaned = code.replaceAll(RegExp(r'[\x00-\x1F\x7F-\x9F]'), '');

    // Remover espaços extras no início e fim
    cleaned = cleaned.trim();

    // Normalizar quebras de linha
    cleaned = cleaned.replaceAll(RegExp(r'\r\n|\r|\n'), '\n');

    // Limitar tamanho
    if (cleaned.length > 4096) {
      cleaned = cleaned.substring(0, 4096);
    }

    return cleaned;
  }

  /// Detecta o tipo de conteúdo com melhor precisão
  Future<String> _detectContentType(String content) async {
    final lowerContent = content.toLowerCase().trim();

    // URLs - detecção mais robusta
    if (_isUrl(content)) return 'url';

    // WiFi
    if (lowerContent.startsWith('wifi:')) return 'wifi';

    // Email
    if (_isEmail(content)) return 'email';

    // Telefone
    if (_isPhone(content)) return 'phone';

    // SMS
    if (lowerContent.startsWith('sms:') || lowerContent.startsWith('smsto:')) {
      return 'sms';
    }

    // vCard
    if (lowerContent.startsWith('begin:vcard')) return 'vcard';

    // Localização
    if (lowerContent.startsWith('geo:')) return 'location';

    // Calendário
    if (lowerContent.startsWith('begin:vevent')) return 'calendar';

    // Texto
    return 'text';
  }

  /// Verifica se é uma URL com melhor precisão
  bool _isUrl(String content) {
    // URLs com protocolo
    if (content.toLowerCase().startsWith(RegExp(r'https?://|ftp://'))) {
      return true;
    }

    // URLs sem protocolo mas com domínio válido
    final domainPattern = RegExp(
      r'^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}',
      caseSensitive: false,
    );

    if (domainPattern.hasMatch(content)) {
      return true;
    }

    // URLs com www
    if (content.toLowerCase().startsWith('www.') && content.contains('.')) {
      return true;
    }

    return false;
  }

  /// Verifica se é um email
  bool _isEmail(String content) {
    if (content.toLowerCase().startsWith('mailto:')) return true;

    final emailPattern = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );

    return emailPattern.hasMatch(content.trim());
  }

  /// Verifica se é um telefone
  bool _isPhone(String content) {
    if (content.toLowerCase().startsWith('tel:')) return true;

    // Remover espaços e caracteres especiais para análise
    final cleanPhone = content.replaceAll(RegExp(r'[\s\-\(\)\+]'), '');

    // Verificar se tem apenas números e tamanho adequado
    if (RegExp(r'^\d{8,15}$').hasMatch(cleanPhone)) {
      return true;
    }

    // Verificar formato internacional
    if (RegExp(
      r'^\+\d{8,15}$',
    ).hasMatch(content.replaceAll(RegExp(r'[\s\-\(\)]'), ''))) {
      return true;
    }

    return false;
  }

  /// Gera descrição melhorada
  Future<String?> _generateDescription(String content, String type) async {
    try {
      switch (type) {
        case 'url':
          final uri = Uri.tryParse(
            content.startsWith('http') ? content : 'https://$content',
          );
          if (uri != null && uri.host.isNotEmpty) {
            return 'Site: ${uri.host}';
          }
          return 'Link da web';

        case 'wifi':
          final ssidMatch = RegExp(r'S:([^;]+)').firstMatch(content);
          if (ssidMatch != null) {
            final ssid = ssidMatch.group(1);
            final securityMatch = RegExp(r'T:([^;]+)').firstMatch(content);
            final security = securityMatch?.group(1) ?? 'Desconhecida';
            return 'WiFi: $ssid ($security)';
          }
          return 'Rede WiFi';

        case 'email':
          final email = content.replaceFirst('mailto:', '');
          return 'Email: $email';

        case 'phone':
          final phone = content.replaceFirst('tel:', '');
          return 'Telefone: $phone';

        case 'sms':
          return 'Mensagem SMS';

        case 'vcard':
          // Tentar extrair nome do vCard
          final nameMatch = RegExp(r'FN:([^\n\r]+)').firstMatch(content);
          if (nameMatch != null) {
            return 'Contato: ${nameMatch.group(1)}';
          }
          return 'Cartão de visita';

        case 'location':
          return 'Localização GPS';

        case 'calendar':
          // Tentar extrair título do evento
          final titleMatch = RegExp(r'SUMMARY:([^\n\r]+)').firstMatch(content);
          if (titleMatch != null) {
            return 'Evento: ${titleMatch.group(1)}';
          }
          return 'Evento de calendário';

        default:
          if (content.length > 50) {
            return 'Texto: ${content.substring(0, 47)}...';
          }
          return 'Texto: $content';
      }
    } catch (e) {
      return 'Conteúdo QR Code';
    }
  }

  /// Fornece feedback baseado no nível de segurança
  Future<void> _provideFeedback(SecurityLevel level) async {
    try {
      // Vibração se habilitada
      if (await _dataService.isVibrationEnabled()) {
        switch (level) {
          case SecurityLevel.safe:
            HapticFeedback.lightImpact();
            break;
          case SecurityLevel.warning:
            HapticFeedback.mediumImpact();
            await Future.delayed(const Duration(milliseconds: 100));
            HapticFeedback.lightImpact();
            break;
          case SecurityLevel.danger:
            HapticFeedback.heavyImpact();
            await Future.delayed(const Duration(milliseconds: 150));
            HapticFeedback.heavyImpact();
            await Future.delayed(const Duration(milliseconds: 150));
            HapticFeedback.heavyImpact();
            break;
          case SecurityLevel.unknown:
            HapticFeedback.selectionClick();
            break;
        }
      }

      // Som se habilitado
      if (await _dataService.areSoundEffectsEnabled()) {
        switch (level) {
          case SecurityLevel.safe:
            SystemSound.play(SystemSoundType.click);
            break;
          case SecurityLevel.warning:
            SystemSound.play(SystemSoundType.click);
            break;
          case SecurityLevel.danger:
            SystemSound.play(SystemSoundType.alert);
            break;
          case SecurityLevel.unknown:
            SystemSound.play(SystemSoundType.click);
            break;
        }
      }
    } catch (e) {
      // Ignorar erros de feedback
    }
  }

  /// Adiciona código ao cache
  void _addToCache(String code, {bool extended = false}) {
    final now = DateTime.now();
    _scanCache[code] = now;

    if (!extended) {
      _scanAttempts[code] = 0;
    }

    // Limpar cache antigo
    _cleanCache();
  }

  /// Limpa entradas antigas do cache
  void _cleanCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _scanCache.entries) {
      if (now.difference(entry.value) > _cacheDuration) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _scanCache.remove(key);
      _scanAttempts.remove(key);
    }
  }

  /// Registra tempo de escaneamento para métricas
  void _recordScanTime(Duration duration) {
    _scanTimes.add(duration);

    if (_scanTimes.length > _maxMetrics) {
      _scanTimes.removeAt(0);
    }
  }

  /// Inicia monitoramento de qualidade
  void _startQualityMonitoring() {
    _qualityTimer?.cancel();
    _qualityTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _checkScanQuality();
    });
  }

  /// Verifica qualidade do escaneamento
  void _checkScanQuality() {
    if (_consecutiveFailures > _maxConsecutiveFailures) {
      _onQualityIssue?.call(
        'Muitas falhas consecutivas. Verifique a iluminação e estabilidade.',
      );
      _consecutiveFailures = 0; // Reset
    }
  }

  /// Pausa o escaneamento
  void _pauseScanning() {
    _isScanning = false;
    _controller?.pauseCamera();
  }

  /// Retoma o escaneamento
  void _resumeScanning() {
    if (!_isProcessing) {
      _isScanning = true;
      _controller?.resumeCamera();
    }
  }

  /// Callbacks
  Function(EnhancedScanResult)? _onScanComplete;
  Function(String)? _onScanError;
  Function(String)? _onQualityIssue;

  void setCallbacks({
    Function(EnhancedScanResult)? onScanComplete,
    Function(String)? onScanError,
    Function(String)? onQualityIssue,
  }) {
    _onScanComplete = onScanComplete;
    _onScanError = onScanError;
    _onQualityIssue = onQualityIssue;
  }

  /// Controle de erro
  void _handleScanError(dynamic error) {
    _consecutiveFailures++;
    _onScanError?.call('Erro no scanner: $error');
  }

  /// Obtém métricas de performance
  ScanMetrics getMetrics() {
    final avgTime = _scanTimes.isEmpty
        ? Duration.zero
        : Duration(
            milliseconds:
                _scanTimes
                    .map((d) => d.inMilliseconds)
                    .reduce((a, b) => a + b) ~/
                _scanTimes.length,
          );

    return ScanMetrics(
      averageScanTime: avgTime,
      totalScans: _scanCache.length,
      consecutiveFailures: _consecutiveFailures,
      cacheSize: _scanCache.length,
    );
  }

  /// Limpa todos os dados
  void dispose() {
    _qualityTimer?.cancel();
    _scanCache.clear();
    _scanAttempts.clear();
    _scanTimes.clear();
    _controller = null;
    _isScanning = false;
    _isProcessing = false;
  }
}

/// Resultado aprimorado do escaneamento
class EnhancedScanResult {
  final bool success;
  final ScanItem? scanItem;
  final SecurityResult? securityResult;
  final ScanQuality scanQuality;
  final Duration processingTime;
  final String? error;

  EnhancedScanResult({
    required this.success,
    this.scanItem,
    this.securityResult,
    this.scanQuality = ScanQuality.good,
    this.processingTime = Duration.zero,
    this.error,
  });
}

/// Qualidade do escaneamento
enum ScanQuality { excellent, good, fair, poor }

/// Métricas de performance
class ScanMetrics {
  final Duration averageScanTime;
  final int totalScans;
  final int consecutiveFailures;
  final int cacheSize;

  ScanMetrics({
    required this.averageScanTime,
    required this.totalScans,
    required this.consecutiveFailures,
    required this.cacheSize,
  });
}
