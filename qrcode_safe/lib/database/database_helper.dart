import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/scan_item.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'qr_seguro.db');
    
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Tabela de itens escaneados
    await db.execute('''
      CREATE TABLE scan_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        content TEXT NOT NULL,
        type TEXT NOT NULL,
        status TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        description TEXT,
        isFavorite INTEGER DEFAULT 0
      )
    ''');

    // Tabela de configurações
    await db.execute('''
      CREATE TABLE settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL
      )
    ''');

    // Tabela de estatísticas
    await db.execute('''
      CREATE TABLE statistics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        total_scans INTEGER DEFAULT 0,
        safe_scans INTEGER DEFAULT 0,
        blocked_scans INTEGER DEFAULT 0,
        last_updated INTEGER NOT NULL
      )
    ''');

    // Inserir configurações padrão
    await _insertDefaultSettings(db);
    
    // Inserir estatísticas iniciais
    await _insertInitialStatistics(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Implementar migrações futuras aqui
    if (oldVersion < 2) {
      // Exemplo de migração para versão 2
      // await db.execute('ALTER TABLE scan_items ADD COLUMN new_column TEXT');
    }
  }

  Future<void> _insertDefaultSettings(Database db) async {
    final defaultSettings = {
      'url_verification': 'true',
      'malware_detection': 'true',
      'scan_history': 'true',
      'notifications': 'true',
      'auto_scan': 'false',
      'vibration': 'true',
      'sound_effects': 'true',
      'theme_mode': 'system',
    };

    for (final entry in defaultSettings.entries) {
      await db.insert('settings', {
        'key': entry.key,
        'value': entry.value,
      });
    }
  }

  Future<void> _insertInitialStatistics(Database db) async {
    await db.insert('statistics', {
      'total_scans': 0,
      'safe_scans': 0,
      'blocked_scans': 0,
      'last_updated': DateTime.now().millisecondsSinceEpoch,
    });
  }

  // CRUD para ScanItems
  Future<int> insertScanItem(ScanItem item) async {
    final db = await database;
    final id = await db.insert('scan_items', item.toMap());
    await _updateStatistics(item.status);
    return id;
  }

  Future<List<ScanItem>> getAllScanItems() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'scan_items',
      orderBy: 'timestamp DESC',
    );
    return List.generate(maps.length, (i) => ScanItem.fromMap(maps[i]));
  }

  Future<List<ScanItem>> getScanItemsByType(String type) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'scan_items',
      where: 'type = ?',
      whereArgs: [type],
      orderBy: 'timestamp DESC',
    );
    return List.generate(maps.length, (i) => ScanItem.fromMap(maps[i]));
  }

  Future<List<ScanItem>> getScanItemsByStatus(String status) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'scan_items',
      where: 'status = ?',
      whereArgs: [status],
      orderBy: 'timestamp DESC',
    );
    return List.generate(maps.length, (i) => ScanItem.fromMap(maps[i]));
  }

  Future<List<ScanItem>> getFavoriteScanItems() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'scan_items',
      where: 'isFavorite = ?',
      whereArgs: [1],
      orderBy: 'timestamp DESC',
    );
    return List.generate(maps.length, (i) => ScanItem.fromMap(maps[i]));
  }

  Future<List<ScanItem>> searchScanItems(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'scan_items',
      where: 'content LIKE ? OR description LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'timestamp DESC',
    );
    return List.generate(maps.length, (i) => ScanItem.fromMap(maps[i]));
  }

  Future<int> updateScanItem(ScanItem item) async {
    final db = await database;
    return await db.update(
      'scan_items',
      item.toMap(),
      where: 'id = ?',
      whereArgs: [item.id],
    );
  }

  Future<int> deleteScanItem(int id) async {
    final db = await database;
    return await db.delete(
      'scan_items',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<int> clearAllScanItems() async {
    final db = await database;
    final result = await db.delete('scan_items');
    await _resetStatistics();
    return result;
  }

  // Configurações
  Future<String?> getSetting(String key) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'settings',
      where: 'key = ?',
      whereArgs: [key],
    );
    return maps.isNotEmpty ? maps.first['value'] : null;
  }

  Future<int> setSetting(String key, String value) async {
    final db = await database;
    return await db.insert(
      'settings',
      {'key': key, 'value': value},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<Map<String, String>> getAllSettings() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('settings');
    return Map.fromEntries(
      maps.map((map) => MapEntry(map['key'], map['value'])),
    );
  }

  // Estatísticas
  Future<Map<String, int>> getStatistics() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('statistics');
    if (maps.isNotEmpty) {
      final stats = maps.first;
      return {
        'total_scans': stats['total_scans'],
        'safe_scans': stats['safe_scans'],
        'blocked_scans': stats['blocked_scans'],
      };
    }
    return {'total_scans': 0, 'safe_scans': 0, 'blocked_scans': 0};
  }

  Future<void> _updateStatistics(String status) async {
    final db = await database;
    final stats = await getStatistics();
    
    final newStats = {
      'total_scans': stats['total_scans']! + 1,
      'safe_scans': status == 'safe' ? stats['safe_scans']! + 1 : stats['safe_scans']!,
      'blocked_scans': status == 'blocked' ? stats['blocked_scans']! + 1 : stats['blocked_scans']!,
      'last_updated': DateTime.now().millisecondsSinceEpoch,
    };

    await db.update('statistics', newStats, where: 'id = 1');
  }

  Future<void> _resetStatistics() async {
    final db = await database;
    await db.update('statistics', {
      'total_scans': 0,
      'safe_scans': 0,
      'blocked_scans': 0,
      'last_updated': DateTime.now().millisecondsSinceEpoch,
    }, where: 'id = 1');
  }

  // Fechar banco de dados
  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
