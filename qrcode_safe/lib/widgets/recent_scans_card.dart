import 'package:flutter/material.dart';

class RecentScansCard extends StatelessWidget {
  const RecentScansCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Scans',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // TODO: Navigate to full history
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildScanItem(
              context,
              icon: Icons.link,
              title: 'Website Link',
              subtitle: 'https://example.com',
              time: '2 hours ago',
              status: ScanStatus.safe,
            ),
            const Divider(height: 24),
            _buildScanItem(
              context,
              icon: Icons.wifi,
              title: 'WiFi Network',
              subtitle: 'Coffee Shop WiFi',
              time: '1 day ago',
              status: ScanStatus.safe,
            ),
            const Divider(height: 24),
            _buildScanItem(
              context,
              icon: Icons.warning,
              title: 'Suspicious Link',
              subtitle: 'Blocked malicious URL',
              time: '3 days ago',
              status: ScanStatus.blocked,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScanItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required String time,
    required ScanStatus status,
  }) {
    Color statusColor;
    IconData statusIcon;
    
    switch (status) {
      case ScanStatus.safe:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case ScanStatus.warning:
        statusColor = Colors.orange;
        statusIcon = Icons.warning;
        break;
      case ScanStatus.blocked:
        statusColor = Colors.red;
        statusIcon = Icons.block;
        break;
    }

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 20,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),
              Text(
                time,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
        Icon(
          statusIcon,
          size: 20,
          color: statusColor,
        ),
      ],
    );
  }
}

enum ScanStatus {
  safe,
  warning,
  blocked,
}
