import 'package:flutter/material.dart';
import '../services/security_analyzer.dart';

class SecurityDetailsWidget extends StatelessWidget {
  final SecurityResult securityResult;

  const SecurityDetailsWidget({
    super.key,
    required this.securityResult,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Status principal
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: _getStatusColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _getStatusColor().withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                _getStatusIcon(),
                color: _getStatusColor(),
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  securityResult.message,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: _getStatusColor(),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Detalhes se houver
        if (securityResult.details.isNotEmpty) ...[
          const SizedBox(height: 12),
          Text(
            'Detalhes:',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            securityResult.details,
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],

        // Riscos identificados
        if (securityResult.risks.isNotEmpty) ...[
          const SizedBox(height: 12),
          Text(
            'Riscos Identificados:',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 4),
          ...securityResult.risks.map((risk) => Padding(
                padding: const EdgeInsets.only(bottom: 2),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(
                      Icons.warning,
                      size: 12,
                      color: Colors.red,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        risk,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.red,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],

        // Recomendações
        if (securityResult.recommendations.isNotEmpty) ...[
          const SizedBox(height: 12),
          Text(
            'Recomendações:',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 4),
          ...securityResult.recommendations.map((recommendation) => Padding(
                padding: const EdgeInsets.only(bottom: 2),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(
                      Icons.lightbulb_outline,
                      size: 12,
                      color: Colors.blue,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        recommendation,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.blue,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ],
    );
  }

  Color _getStatusColor() {
    switch (securityResult.level) {
      case SecurityLevel.safe:
        return Colors.green;
      case SecurityLevel.warning:
        return Colors.orange;
      case SecurityLevel.danger:
        return Colors.red;
      case SecurityLevel.unknown:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon() {
    switch (securityResult.level) {
      case SecurityLevel.safe:
        return Icons.verified_user;
      case SecurityLevel.warning:
        return Icons.warning;
      case SecurityLevel.danger:
        return Icons.dangerous;
      case SecurityLevel.unknown:
        return Icons.help_outline;
    }
  }
}
