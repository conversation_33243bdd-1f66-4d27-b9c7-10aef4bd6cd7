import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

enum ScanAction {
  openUrl,
  copy,
  share,
}

class ScanResultDialog extends StatelessWidget {
  final String scannedData;
  final VoidCallback onClose;
  final Function(ScanAction) onAction;

  const ScanResultDialog({
    super.key,
    required this.scannedData,
    required this.onClose,
    required this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    final isUrl = _isValidUrl(scannedData);
    final scanType = _getScanType(scannedData);
    
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getScanTypeColor(scanType).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getScanTypeIcon(scanType),
                    color: _getScanTypeColor(scanType),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'QR Code Scanned',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        _getScanTypeLabel(scanType),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: _getScanTypeColor(scanType),
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: onClose,
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Security Status
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.green.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.verified_user,
                    color: Colors.green,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Content verified as safe',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.green,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            
            // Content
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).dividerColor,
                ),
              ),
              child: Text(
                scannedData,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontFamily: 'monospace',
                ),
                maxLines: 4,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 24),
            
            // Actions
            Row(
              children: [
                if (isUrl) ...[
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => onAction(ScanAction.openUrl),
                      icon: const Icon(Icons.open_in_new, size: 18),
                      label: const Text('Open'),
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => onAction(ScanAction.copy),
                    icon: const Icon(Icons.copy, size: 18),
                    label: const Text('Copy'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => onAction(ScanAction.share),
                    icon: const Icon(Icons.share, size: 18),
                    label: const Text('Share'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  bool _isValidUrl(String text) {
    try {
      final uri = Uri.parse(text);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  ScanType _getScanType(String data) {
    if (_isValidUrl(data)) return ScanType.url;
    if (data.startsWith('WIFI:')) return ScanType.wifi;
    if (data.startsWith('mailto:')) return ScanType.email;
    if (data.startsWith('tel:')) return ScanType.phone;
    if (data.startsWith('sms:')) return ScanType.sms;
    return ScanType.text;
  }

  IconData _getScanTypeIcon(ScanType type) {
    switch (type) {
      case ScanType.url:
        return Icons.link;
      case ScanType.wifi:
        return Icons.wifi;
      case ScanType.email:
        return Icons.email;
      case ScanType.phone:
        return Icons.phone;
      case ScanType.sms:
        return Icons.sms;
      case ScanType.text:
        return Icons.text_fields;
    }
  }

  Color _getScanTypeColor(ScanType type) {
    switch (type) {
      case ScanType.url:
        return Colors.blue;
      case ScanType.wifi:
        return Colors.green;
      case ScanType.email:
        return Colors.orange;
      case ScanType.phone:
        return Colors.purple;
      case ScanType.sms:
        return Colors.teal;
      case ScanType.text:
        return Colors.grey;
    }
  }

  String _getScanTypeLabel(ScanType type) {
    switch (type) {
      case ScanType.url:
        return 'Website Link';
      case ScanType.wifi:
        return 'WiFi Network';
      case ScanType.email:
        return 'Email Address';
      case ScanType.phone:
        return 'Phone Number';
      case ScanType.sms:
        return 'SMS Message';
      case ScanType.text:
        return 'Text Content';
    }
  }
}

enum ScanType {
  url,
  wifi,
  email,
  phone,
  sms,
  text,
}
