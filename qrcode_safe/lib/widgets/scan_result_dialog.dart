import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/scan_item.dart';
import '../services/security_analyzer.dart';
import 'security_details_widget.dart';

enum ScanAction { openUrl, copy, share }

class ScanResultDialog extends StatelessWidget {
  final ScanItem scanItem;
  final SecurityResult? securityResult;
  final VoidCallback onClose;
  final Function(ScanAction) onAction;

  const ScanResultDialog({
    super.key,
    required this.scanItem,
    this.securityResult,
    required this.onClose,
    required this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    final isUrl = _isValidUrl(scanItem.content);
    final scanType = _getScanTypeFromString(scanItem.type);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getScanTypeColor(scanType).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getScanTypeIcon(scanType),
                    color: _getScanTypeColor(scanType),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'QR Code Escaneado',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.w600),
                      ),
                      Text(
                        _getScanTypeLabel(scanType),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: _getScanTypeColor(scanType),
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(icon: const Icon(Icons.close), onPressed: onClose),
              ],
            ),
            const SizedBox(height: 16),

            // Security Status
            if (securityResult != null)
              SecurityDetailsWidget(securityResult: securityResult!)
            else
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getSecurityColor(
                    scanItem.status,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _getSecurityColor(
                      scanItem.status,
                    ).withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getSecurityIcon(scanItem.status),
                      color: _getSecurityColor(scanItem.status),
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _getSecurityMessage(scanItem.status),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: _getSecurityColor(scanItem.status),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            const SizedBox(height: 16),

            // Content
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Theme.of(context).dividerColor),
              ),
              child: Text(
                scanItem.content,
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontFamily: 'monospace'),
                maxLines: 4,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 24),

            // Actions
            Row(
              children: [
                if (isUrl) ...[
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => onAction(ScanAction.openUrl),
                      icon: const Icon(Icons.open_in_new, size: 18),
                      label: const Text('Abrir'),
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => onAction(ScanAction.copy),
                    icon: const Icon(Icons.copy, size: 18),
                    label: const Text('Copiar'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => onAction(ScanAction.share),
                    icon: const Icon(Icons.share, size: 18),
                    label: const Text('Compartilhar'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  bool _isValidUrl(String text) {
    try {
      final uri = Uri.parse(text);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  ScanType _getScanType(String data) {
    if (_isValidUrl(data)) return ScanType.url;
    if (data.startsWith('WIFI:')) return ScanType.wifi;
    if (data.startsWith('mailto:')) return ScanType.email;
    if (data.startsWith('tel:')) return ScanType.phone;
    if (data.startsWith('sms:')) return ScanType.sms;
    return ScanType.text;
  }

  IconData _getScanTypeIcon(ScanType type) {
    switch (type) {
      case ScanType.url:
        return Icons.link;
      case ScanType.wifi:
        return Icons.wifi;
      case ScanType.email:
        return Icons.email;
      case ScanType.phone:
        return Icons.phone;
      case ScanType.sms:
        return Icons.sms;
      case ScanType.text:
        return Icons.text_fields;
    }
  }

  Color _getScanTypeColor(ScanType type) {
    switch (type) {
      case ScanType.url:
        return Colors.blue;
      case ScanType.wifi:
        return Colors.green;
      case ScanType.email:
        return Colors.orange;
      case ScanType.phone:
        return Colors.purple;
      case ScanType.sms:
        return Colors.teal;
      case ScanType.text:
        return Colors.grey;
    }
  }

  String _getScanTypeLabel(ScanType type) {
    switch (type) {
      case ScanType.url:
        return 'Link do Site';
      case ScanType.wifi:
        return 'Rede WiFi';
      case ScanType.email:
        return 'Endereço de Email';
      case ScanType.phone:
        return 'Número de Telefone';
      case ScanType.sms:
        return 'Mensagem SMS';
      case ScanType.text:
        return 'Conteúdo de Texto';
    }
  }

  ScanType _getScanTypeFromString(String type) {
    switch (type.toLowerCase()) {
      case 'url':
        return ScanType.url;
      case 'wifi':
        return ScanType.wifi;
      case 'email':
        return ScanType.email;
      case 'phone':
        return ScanType.phone;
      case 'sms':
        return ScanType.sms;
      default:
        return ScanType.text;
    }
  }

  String _getSecurityMessage(String status) {
    switch (status) {
      case 'safe':
        return 'Conteúdo verificado como seguro';
      case 'warning':
        return 'Conteúdo requer atenção';
      case 'blocked':
        return 'Conteúdo bloqueado por segurança';
      default:
        return 'Status de segurança desconhecido';
    }
  }

  Color _getSecurityColor(String status) {
    switch (status) {
      case 'safe':
        return Colors.green;
      case 'warning':
        return Colors.orange;
      case 'blocked':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getSecurityIcon(String status) {
    switch (status) {
      case 'safe':
        return Icons.verified_user;
      case 'warning':
        return Icons.warning;
      case 'blocked':
        return Icons.dangerous;
      default:
        return Icons.help_outline;
    }
  }

  Color _getSecurityColorFromLevel(SecurityLevel level) {
    switch (level) {
      case SecurityLevel.safe:
        return Colors.green;
      case SecurityLevel.warning:
        return Colors.orange;
      case SecurityLevel.danger:
        return Colors.red;
      case SecurityLevel.unknown:
        return Colors.grey;
    }
  }
}

enum ScanType { url, wifi, email, phone, sms, text }
