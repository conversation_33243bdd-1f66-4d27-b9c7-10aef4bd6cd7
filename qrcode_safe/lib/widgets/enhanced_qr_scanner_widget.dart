import 'package:flutter/material.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import '../services/enhanced_qr_scanner.dart';

class EnhancedQRScannerWidget extends StatefulWidget {
  final Function(EnhancedScanResult) onScanComplete;
  final Function(String)? onError;

  const EnhancedQRScannerWidget({
    super.key,
    required this.onScanComplete,
    this.onError,
  });

  @override
  State<EnhancedQRScannerWidget> createState() => _EnhancedQRScannerWidgetState();
}

class _EnhancedQRScannerWidgetState extends State<EnhancedQRScannerWidget>
    with TickerProviderStateMixin {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  final EnhancedQRScanner _scanner = EnhancedQRScanner();
  
  QRViewController? controller;
  bool isFlashOn = false;
  bool isScanning = true;
  String? statusMessage;
  ScanQuality currentQuality = ScanQuality.good;
  
  // Animações
  late AnimationController _scanLineController;
  late AnimationController _pulseController;
  late Animation<double> _scanLineAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupScanner();
  }

  void _initializeAnimations() {
    // Animação da linha de escaneamento
    _scanLineController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _scanLineAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scanLineController,
      curve: Curves.easeInOut,
    ));

    // Animação de pulso para feedback
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.elasticOut,
    ));

    _scanLineController.repeat(reverse: true);
  }

  void _setupScanner() {
    _scanner.setCallbacks(
      onScanComplete: (result) {
        _handleScanComplete(result);
      },
      onScanError: (error) {
        _handleScanError(error);
      },
      onQualityIssue: (message) {
        _showQualityMessage(message);
      },
    );
  }

  void _handleScanComplete(EnhancedScanResult result) {
    setState(() {
      currentQuality = result.scanQuality;
    });
    
    // Animação de sucesso
    _pulseController.forward().then((_) {
      _pulseController.reverse();
    });
    
    widget.onScanComplete(result);
  }

  void _handleScanError(String error) {
    setState(() {
      statusMessage = error;
    });
    
    widget.onError?.call(error);
    
    // Limpar mensagem após alguns segundos
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          statusMessage = null;
        });
      }
    });
  }

  void _showQualityMessage(String message) {
    setState(() {
      statusMessage = message;
    });
    
    // Limpar mensagem após alguns segundos
    Future.delayed(const Duration(seconds: 4), () {
      if (mounted) {
        setState(() {
          statusMessage = null;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('Escanear QR Code'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(isFlashOn ? Icons.flash_on : Icons.flash_off),
            onPressed: _toggleFlash,
          ),
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showScanInfo,
          ),
        ],
      ),
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // Scanner view
          QRView(
            key: qrKey,
            onQRViewCreated: _onQRViewCreated,
            overlay: QrScannerOverlayShape(
              borderColor: _getBorderColor(),
              borderRadius: 16,
              borderLength: 40,
              borderWidth: 4,
              cutOutSize: 280,
            ),
          ),
          
          // Linha de escaneamento animada
          _buildScanLine(),
          
          // Indicador de qualidade
          _buildQualityIndicator(),
          
          // Mensagem de status
          if (statusMessage != null) _buildStatusMessage(),
          
          // Instruções
          _buildInstructions(),
          
          // Controles inferiores
          _buildBottomControls(),
        ],
      ),
    );
  }

  Widget _buildScanLine() {
    return Positioned.fill(
      child: Center(
        child: Container(
          width: 280,
          height: 280,
          child: AnimatedBuilder(
            animation: _scanLineAnimation,
            builder: (context, child) {
              return CustomPaint(
                painter: ScanLinePainter(
                  progress: _scanLineAnimation.value,
                  color: _getBorderColor(),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildQualityIndicator() {
    return Positioned(
      top: 120,
      right: 20,
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _pulseAnimation.value,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _getQualityColor().withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _getQualityIcon(),
                    color: Colors.white,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _getQualityText(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatusMessage() {
    return Positioned(
      top: 160,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          statusMessage!,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 180,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.qr_code_scanner,
              color: Colors.white,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              'Posicione o QR code dentro da moldura',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              'Mantenha o dispositivo estável para melhor leitura',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return Positioned(
      bottom: 60,
      left: 20,
      right: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildControlButton(
            icon: Icons.image,
            label: 'Galeria',
            onPressed: _pickFromGallery,
          ),
          _buildControlButton(
            icon: isScanning ? Icons.pause : Icons.play_arrow,
            label: isScanning ? 'Pausar' : 'Continuar',
            onPressed: _toggleScanning,
          ),
          _buildControlButton(
            icon: Icons.flip_camera_ios,
            label: 'Virar',
            onPressed: _flipCamera,
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.6),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: Icon(icon, color: Colors.white),
            onPressed: onPressed,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Color _getBorderColor() {
    switch (currentQuality) {
      case ScanQuality.excellent:
        return Colors.green;
      case ScanQuality.good:
        return Colors.blue;
      case ScanQuality.fair:
        return Colors.orange;
      case ScanQuality.poor:
        return Colors.red;
    }
  }

  Color _getQualityColor() {
    switch (currentQuality) {
      case ScanQuality.excellent:
        return Colors.green;
      case ScanQuality.good:
        return Colors.blue;
      case ScanQuality.fair:
        return Colors.orange;
      case ScanQuality.poor:
        return Colors.red;
    }
  }

  IconData _getQualityIcon() {
    switch (currentQuality) {
      case ScanQuality.excellent:
        return Icons.signal_cellular_4_bar;
      case ScanQuality.good:
        return Icons.signal_cellular_3_bar;
      case ScanQuality.fair:
        return Icons.signal_cellular_2_bar;
      case ScanQuality.poor:
        return Icons.signal_cellular_1_bar;
    }
  }

  String _getQualityText() {
    switch (currentQuality) {
      case ScanQuality.excellent:
        return 'Excelente';
      case ScanQuality.good:
        return 'Boa';
      case ScanQuality.fair:
        return 'Regular';
      case ScanQuality.poor:
        return 'Ruim';
    }
  }

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    _scanner.initialize(controller);
  }

  void _toggleFlash() async {
    if (controller != null) {
      await controller!.toggleFlash();
      setState(() {
        isFlashOn = !isFlashOn;
      });
    }
  }

  void _toggleScanning() {
    setState(() {
      isScanning = !isScanning;
    });
    
    if (isScanning) {
      controller?.resumeCamera();
      _scanLineController.repeat(reverse: true);
    } else {
      controller?.pauseCamera();
      _scanLineController.stop();
    }
  }

  void _flipCamera() async {
    if (controller != null) {
      await controller!.flipCamera();
    }
  }

  void _pickFromGallery() {
    // TODO: Implementar seleção de imagem da galeria
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Seleção da galeria em breve...'),
      ),
    );
  }

  void _showScanInfo() {
    final metrics = _scanner.getMetrics();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Informações do Scanner'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Tempo médio: ${metrics.averageScanTime.inMilliseconds}ms'),
            Text('Total de scans: ${metrics.totalScans}'),
            Text('Falhas consecutivas: ${metrics.consecutiveFailures}'),
            Text('Cache: ${metrics.cacheSize} itens'),
            const SizedBox(height: 8),
            Text('Qualidade atual: ${_getQualityText()}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fechar'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _scanLineController.dispose();
    _pulseController.dispose();
    controller?.dispose();
    _scanner.dispose();
    super.dispose();
  }
}

/// Painter para a linha de escaneamento animada
class ScanLinePainter extends CustomPainter {
  final double progress;
  final Color color;

  ScanLinePainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withValues(alpha: 0.8)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final y = size.height * progress;
    
    // Linha principal
    canvas.drawLine(
      Offset(20, y),
      Offset(size.width - 20, y),
      paint,
    );
    
    // Efeito de brilho
    final glowPaint = Paint()
      ..color = color.withValues(alpha: 0.3)
      ..strokeWidth = 6
      ..style = PaintingStyle.stroke;
    
    canvas.drawLine(
      Offset(20, y),
      Offset(size.width - 20, y),
      glowPaint,
    );
  }

  @override
  bool shouldRepaint(ScanLinePainter oldDelegate) {
    return oldDelegate.progress != progress || oldDelegate.color != color;
  }
}
