{"name": "url_launcher_ios", "version": "0.0.1", "summary": "Flutter plugin for launching a URL.", "description": "A Flutter plugin for making the underlying platform (Android or iOS) launch a URL.", "homepage": "https://github.com/flutter/packages/tree/main/packages/url_launcher", "license": {"type": "BSD", "file": "../LICENSE"}, "authors": {"Flutter Dev Team": "<EMAIL>"}, "source": {"http": "https://github.com/flutter/packages/tree/main/packages/url_launcher/url_launcher_ios"}, "documentation_url": "https://pub.dev/packages/url_launcher", "swift_versions": "5.0", "source_files": "url_launcher_ios/Sources/**/*.swift", "xcconfig": {"LIBRARY_SEARCH_PATHS": "$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)/ $(SDKROOT)/usr/lib/swift", "LD_RUNPATH_SEARCH_PATHS": "/usr/lib/swift"}, "dependencies": {"Flutter": []}, "platforms": {"ios": "12.0"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}, "resource_bundles": {"url_launcher_ios_privacy": ["url_launcher_ios/Sources/url_launcher_ios/Resources/PrivacyInfo.xcprivacy"]}, "swift_version": "5.0"}