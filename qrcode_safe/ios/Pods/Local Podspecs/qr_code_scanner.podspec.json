{"name": "qr_code_scanner", "version": "0.2.0", "summary": "QR Code Scanner for flutter.", "description": "A new Flutter project.", "homepage": "https://github.com/juliuscanute/qr_code_scanner", "license": {"file": "../LICENSE"}, "authors": {"Your Company": "juliuscanute[*]touchcapture.net"}, "source": {"path": "."}, "source_files": "Classes/**/*", "public_header_files": "Classes/**/*.h", "dependencies": {"Flutter": [], "MTBBarcodeScanner": []}, "platforms": {"ios": "8.0"}, "swift_versions": "4.0", "swift_version": "4.0"}