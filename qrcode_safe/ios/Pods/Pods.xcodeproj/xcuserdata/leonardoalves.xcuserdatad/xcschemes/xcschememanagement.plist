<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SchemeUserState</key>
	<dict>
		<key>Flutter.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>MTBBarcodeScanner.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-Runner.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-RunnerTests.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>permission_handler_apple-permission_handler_apple_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>permission_handler_apple.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>qr_code_scanner.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>shared_preferences_foundation-shared_preferences_foundation_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>shared_preferences_foundation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>url_launcher_ios-url_launcher_ios_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>url_launcher_ios.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
	</dict>
	<key>SuppressBuildableAutocreation</key>
	<dict/>
</dict>
</plist>
