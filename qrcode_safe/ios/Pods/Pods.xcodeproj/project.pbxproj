// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXAggregateTarget section */
		1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 94078A06C17E946ADC1F2C06726219E5 /* Build configuration list for PBXAggregateTarget "Flutter" */;
			buildPhases = (
			);
			dependencies = (
			);
			name = Flutter;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		03432570AB88998A035398D46B01F04C /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */; };
		036DB75E39D0F256E8F12DB66249E151 /* MTBBarcodeScanner.m in Sources */ = {isa = PBXBuildFile; fileRef = 19942BB0CC239C074112446425810856 /* MTBBarcodeScanner.m */; };
		0683FB6A0EA2EF47F0B322524F95133A /* URLLaunchSession.swift in Sources */ = {isa = PBXBuildFile; fileRef = F682705073E974C61A51F88C202D15CC /* URLLaunchSession.swift */; };
		0AE22FF7663B5976F724157B0B55DD56 /* AppTrackingTransparencyPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 8BB0FC92B6D33B68FFB49CA3ABB60EB0 /* AppTrackingTransparencyPermissionStrategy.m */; };
		0C67EF1B7B35D3378528B60173E29CA9 /* PermissionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 56F49ABAEE03DCC38F0D92B0CD9E9827 /* PermissionManager.m */; };
		0E8149EAEA1794AC30BFB6B95E305546 /* PermissionHandlerPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = 65F7A10D6F94FD6BDC89FAFD865C8FFF /* PermissionHandlerPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0EBF612BD9A9D28065172409F6498539 /* CriticalAlertsPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 5AA99A0261622A434365187FD8F519EF /* CriticalAlertsPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0F21C51CEF0C472E416D78035F9817A6 /* SpeechPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 665227A32ADA8E64F557EE07C846FB37 /* SpeechPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		10ED346DDDB313562C91F8E5A7EA19A7 /* PhotoPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 30EB37C2078C48C1E0A6D32CCD23A84F /* PhotoPermissionStrategy.m */; };
		121679B8561F25B70F958E1C449754C5 /* Codec.h in Headers */ = {isa = PBXBuildFile; fileRef = BE05F9BC7EF24B8C7CE0744D0D039925 /* Codec.h */; settings = {ATTRIBUTES = (Public, ); }; };
		12B38331E4F92C9745F0FF0F3FE8C1B6 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */; };
		19B553C8B2E45D962CA48D89000E09DE /* Pods-Runner-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 4CB131F3BC1D6D2C79680B59CF30EF6B /* Pods-Runner-dummy.m */; };
		26FDC6CCF00A736B47E27A0B325FC8AB /* PhotoPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = FFAF7031D64D5046D86C2F180D2E308B /* PhotoPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2C8CCA1A7610BA60433377EE91A98CBC /* LocationPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 5568ABAE5F46EBB3C8E48B9E0C5D48BD /* LocationPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2E585A6D3EB862CB358F4236571AF949 /* AssistantPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = CBF60097BD84F1E2284D5E2AFC8EA642 /* AssistantPermissionStrategy.m */; };
		309549EA2063A75373A0B3580CF8C424 /* MTBBarcodeScanner-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 4C3698AA06F487DB27EBF02A2BE1220B /* MTBBarcodeScanner-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3875191124A81F2B1F777FA0F3B4B732 /* CriticalAlertsPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 42D244B224C323C72F50E471E7EDAA41 /* CriticalAlertsPermissionStrategy.m */; };
		4305445D8E6067B09985A36FAF7E1C36 /* UnknownPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = F50ED5DF2A1A9902D41E6CD0644D7327 /* UnknownPermissionStrategy.m */; };
		46AC153588A74102B5C92C37038834AF /* EventPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = F74A98ABA8B82A390D306C589A96545D /* EventPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		48ABC9CC212358D168731AEE56729598 /* shared_preferences_foundation-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 78B952E3264769CE1C28EB4C6EAE9446 /* shared_preferences_foundation-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4A43F54128D0FDF0B93C48446CE89B1D /* permission_handler_apple-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 949100FEE0EC46F03CAB377A7576626A /* permission_handler_apple-dummy.m */; };
		4D4CFC73824D102E82A8F3B0EBF43D39 /* ContactPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 3448DBF71902BD6910D8FEF896BD424D /* ContactPermissionStrategy.m */; };
		58FF267CBDAA4BDAA8279F31A9CB3C40 /* url_launcher_ios-url_launcher_ios_privacy in Resources */ = {isa = PBXBuildFile; fileRef = DBA6F34C072B134D3BE38983776DD1C3 /* url_launcher_ios-url_launcher_ios_privacy */; };
		5B06A55C0BD6EC7C93369250069299CA /* messages.g.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2C0BAD9CD71DD8377EB97F64A55F7255 /* messages.g.swift */; };
		5C41E2EB060B6981260C5BBA07FE575C /* EventPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 530C6C1A6F1E8056EBCED03F3E864F05 /* EventPermissionStrategy.m */; };
		6AC94655CFA316F8DC44B632AE169181 /* PhonePermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = B954F384544AEE9A82B8000480F7C8EC /* PhonePermissionStrategy.m */; };
		6C9A77B43CA0635F9E3B987759476CFE /* UnknownPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = D826AB59FE76AF44322D97703DD0590F /* UnknownPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6F6257A610B1AB690E38F1CF7E9AF3F9 /* LocationPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = C1F63004BE8D5A35263B6FB70141E104 /* LocationPermissionStrategy.m */; };
		718A4F8C77639F1A12F101A9E2EE5E04 /* MTBBarcodeScanner.h in Headers */ = {isa = PBXBuildFile; fileRef = CE80AEE54213307F43286027D20CA42C /* MTBBarcodeScanner.h */; settings = {ATTRIBUTES = (Public, ); }; };
		71F3553FFF2641F08CB6EEBEBE0AD73C /* Pods-Runner-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = BCC84BDE7260B712B097666E169A193C /* Pods-Runner-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		74AE7AA5E923BC7D1F1CE6FB1AF221A2 /* PermissionManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 790F207D667079AB3ACEE730C8D9A74C /* PermissionManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		75DA17B512459AF9817ED928870095AA /* MediaLibraryPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 0F00C6E6905214EA9528DB75D44482EA /* MediaLibraryPermissionStrategy.m */; };
		7778411698137CA267B6213857D67549 /* url_launcher_ios-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 5C12E65822F20EA0FE40CCA3FD20E664 /* url_launcher_ios-dummy.m */; };
		793A16F2F261D1392CE8D447268BDD60 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = EE84B896A27D8CAB0349A3768452DB14 /* PrivacyInfo.xcprivacy */; };
		7C7D84E9240ADF7554BFFEC5DECA4D01 /* PermissionHandlerEnums.h in Headers */ = {isa = PBXBuildFile; fileRef = E633951C848CF6F29BEFEFF26F002EEF /* PermissionHandlerEnums.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7F0345C3434305DFAEF184BBD6641FE1 /* QRViewFactory.swift in Sources */ = {isa = PBXBuildFile; fileRef = 16F60600D7EFA8B40161C84E63220F4A /* QRViewFactory.swift */; };
		8301C3E2174422AC905818E5DD71CA9F /* ContactPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C0BDF52FF6FECE67F727FCB526D6302 /* ContactPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		89B66AD0211DF422E28D4BA0FDFB5182 /* NotificationPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 0B7F1FF61B52EFA422A441B6E914F5C3 /* NotificationPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8A377F9A1E700ACDFE60ECF38E7FBF60 /* SpeechPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = B62A811C47C616E581AC9F81815A04DE /* SpeechPermissionStrategy.m */; };
		8BD319D3AAE00D70F1996967AE54CA82 /* SensorPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = CB5CCAD432EFEE2ACB006BC943F241DB /* SensorPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		97F91860804C4FF0F95BBCAEC6D85BAF /* url_launcher_ios-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = EECBB7DFB0941CA8E5664F05BDC38314 /* url_launcher_ios-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9B8D0A18B5DD029F59DDC582B877B41F /* PhonePermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 245889C4664F78A1A205C821E81E4C69 /* PhonePermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9B9CEE0D664C57FB31CCAA70D76A9E87 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */; };
		9F3376F43893107B68E2FB140B5A2268 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 355D0180080F026E55C27BAC37B41ABA /* AVFoundation.framework */; };
		A0F8833ED8A6AC53A64E844315D69060 /* NotificationPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 61837375FA2BCB14F68D85EBFF0454E4 /* NotificationPermissionStrategy.m */; };
		A20F6A21391880DF157F42E08570FD3E /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 85EAF159AABB5D96C3B51BA3D8EF8FF7 /* PrivacyInfo.xcprivacy */; };
		A2500528640D2211715118237B1E2B91 /* AudioVideoPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = C588489D32FDC33C42C63BB8A3A3E2A4 /* AudioVideoPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7562EAC8F35C4207D6DF1FD21AE7023 /* URLLauncherPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = F5449C617B01870C6A67A69BE87631A5 /* URLLauncherPlugin.swift */; };
		A985D47C7B6B78C64F0211B9E4AA916D /* AssistantPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 112E0388B50192EC223BE1368956BB05 /* AssistantPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A9B4005CE5D708F1BBBD78996E2C897E /* QRView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 980A6251B72ADDBEAC19D8BA314E3C54 /* QRView.swift */; };
		AA2937664B69935CCA0FDA5F26BD6165 /* qr_code_scanner-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = D537A5893A88B49628C1AA1DFCD97964 /* qr_code_scanner-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AB6B59C5DB5C485F5E1C0B3013806CF7 /* permission_handler_apple-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = D50C565374CC523CF6B46DEFCD73D5F0 /* permission_handler_apple-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AB97410CE8410A5B2279DDF4C43D7572 /* Launcher.swift in Sources */ = {isa = PBXBuildFile; fileRef = 93841520DF715A82BA54F0BFD9759C53 /* Launcher.swift */; };
		ADAFD83ACFABC2C9A3A595354E84993B /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 1CFCE387C749144A9CDF65CFF5188BCE /* PrivacyInfo.xcprivacy */; };
		B375EA128E6579366091BAA390BBDD34 /* Pods-RunnerTests-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 8758A56177F57A2EE30894AA6F81B31A /* Pods-RunnerTests-dummy.m */; };
		B7C67D475D63B3D6501863172F8AD35F /* shared_preferences_foundation-shared_preferences_foundation_privacy in Resources */ = {isa = PBXBuildFile; fileRef = 0652FCE3FC19056983AABE058B3CC45B /* shared_preferences_foundation-shared_preferences_foundation_privacy */; };
		BC2C168999E585DBDF5CDD8E9E5217DD /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */; };
		BCE0C8639D6AE4A8792736C0ABB32A69 /* BackgroundRefreshStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 7EE849350B031F9293FF39B2BE018644 /* BackgroundRefreshStrategy.m */; };
		BF0069CAFABF46710A32A9CC4B9D60FA /* MediaLibraryPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = B30388C1FF31B003DDC05B7BBD64447C /* MediaLibraryPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C14990D6EA8F277C8CB3FBBF8E2784EC /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */; };
		C5316A62CBB753EFB2F8E37B58B73F4E /* FlutterQrPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = E6953E238C0C1CB99C3CDC2ACC7580EF /* FlutterQrPlugin.m */; };
		C69611CADFD4A4CBD493FA39DC1E7E66 /* SwiftFlutterQrPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 913E74A677D574BA78CE2BC58164FAD8 /* SwiftFlutterQrPlugin.swift */; };
		CC766C2F06095124222B9EB87BEAEBBD /* FlutterQrPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = 0525459B9CFED0A3CCFDFEA1A6947B97 /* FlutterQrPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		CC781030BA3802C6B47807E5ECCF5C69 /* MTBBarcodeScanner-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 906C5F1E88D3383DA6E6FFE986454BB4 /* MTBBarcodeScanner-dummy.m */; };
		CEBD84922D2CCEF26C272418EC3EB3A6 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */; };
		D2A52C6979E7137696C04242485D21EC /* shared_preferences_foundation-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = EE927CD5B46DAB5884E10036C11F7798 /* shared_preferences_foundation-dummy.m */; };
		D73FD31018FE053DFECE1ADD9091607F /* AppTrackingTransparencyPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 0AC70C1EDC054FACE700CD1494E02AC1 /* AppTrackingTransparencyPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DBC3E1374D6F4F81FC8C6AB91DFD48DB /* StoragePermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 3E0C6DB2269FA4E66744A996E387AC80 /* StoragePermissionStrategy.m */; };
		DD54438BFB24943B91DBDD11A58D0A03 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F9E5AF19473BBB6D6B21A3B29833FDE0 /* QuartzCore.framework */; };
		DF972B5566EB8F594474408D1FEEBE03 /* qr_code_scanner-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 7AD6D0F6201BC8F67087A69CB20CD8CE /* qr_code_scanner-dummy.m */; };
		E1290184B9BE7EF8E2EB64C3C38F39CF /* StoragePermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 21639B22B7A4817A079DFA0186A4C856 /* StoragePermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E1F16FC5464194ABA094471ED06A3373 /* BluetoothPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 2FEF602FB14C96EEA81E5E79F87909FF /* BluetoothPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E517EE53DE3F1D6977E08A2040FB8C5D /* AudioVideoPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = ED9AE874B2ED0ADD8CCA4E36CF53A7C7 /* AudioVideoPermissionStrategy.m */; };
		E6B934071CC8AAC4C2F13418E980DC83 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */; };
		E702B68F1D9F5FBBF77021A57B6642ED /* Codec.m in Sources */ = {isa = PBXBuildFile; fileRef = D0E21B5259420515F976F0832205CC02 /* Codec.m */; };
		E9D09D2A0AE66B8FCF53A3B0B6171793 /* BluetoothPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 2D37A765B5BB843672F488B32BC47EBA /* BluetoothPermissionStrategy.m */; };
		EAD247F99E22F9672E6556A00D0B5C03 /* SharedPreferencesPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFE8DAFF33836AE453B8A4D3EB4B4CB /* SharedPreferencesPlugin.swift */; };
		EB2DC96CDCF638AB89007D2DB0F3119A /* Pods-RunnerTests-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 2CB04EB554430E41E5A19EDE6D62C9F2 /* Pods-RunnerTests-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EBC219C8C0EB22FE57D8CAFD288BB75E /* BackgroundRefreshStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 092D9F017D692F3C5C9136112F1511BE /* BackgroundRefreshStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EBC24A935793317E4E9E956E3A9BF5EA /* messages.g.swift in Sources */ = {isa = PBXBuildFile; fileRef = 12E8299A93E28906F0E519527728B158 /* messages.g.swift */; };
		F0A225DB0B069780B9046F550CF964B7 /* PermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = A586C747E3965EC542D2E8F470FC7721 /* PermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F7DF9728D6CF306F2B6A9A90031BB0A7 /* SensorPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 2804FD97F1F9DF9DF72E5E1C6EC026C6 /* SensorPermissionStrategy.m */; };
		FA6596239E85709C1B9C53EB4D6B8A6F /* PermissionHandlerPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = 773A231F8701E8A75B979E34900CDD39 /* PermissionHandlerPlugin.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		1018EEAB85873500F1E56CA62986FF4D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 517E8BED8B0E6D6FD078BD19B0A13893;
			remoteInfo = "permission_handler_apple-permission_handler_apple_privacy";
		};
		1255552792047A14783EFB2EA9D48CB5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B6AF8B7CEAF6321719ABBC7E770624DA;
			remoteInfo = "shared_preferences_foundation-shared_preferences_foundation_privacy";
		};
		2A7B68A7C970313F3519F2FF3DA9F170 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DF45E85925DF410BE416B32171F59C1F;
			remoteInfo = url_launcher_ios;
		};
		39D96E40726AFCAF3B3811D5F3F258A3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = D83CD42EA57F97A225D24BDC7BD6B6F6;
			remoteInfo = MTBBarcodeScanner;
		};
		593350DEB0F244B69DF14054180EEDF7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		7468773F7B305E832D04EB044D5E8C41 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = D83CD42EA57F97A225D24BDC7BD6B6F6;
			remoteInfo = MTBBarcodeScanner;
		};
		7DA7E80A3E73DB4836E3DC524DB64610 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		883A379E2595F6FF354E7D26C19F17CA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8B74B458B450D74B75744B87BD747314;
			remoteInfo = "Pods-Runner";
		};
		8F6F5B32D68AA95554B2903C502D1F94 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E864A9DA72EED9027036A46ABD1822BC;
			remoteInfo = qr_code_scanner;
		};
		988B4D87DD28277C6E2D693D153A80C3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AB5EE685B22D01885ADD930538E8DD3C;
			remoteInfo = shared_preferences_foundation;
		};
		A3B07104131B5940C9172C5859866AAB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		D138AAA7C726DA897B31B10EB8081E24 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 43BE97C40504244259CF3C1D21E7EDB5;
			remoteInfo = "url_launcher_ios-url_launcher_ios_privacy";
		};
		D26F92AA6B87E3E8F7490E32EF0B83AE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		E87DE1A24A38706E60E4D8199518207B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		E9761E3456CA363CB38036DFB7BF1774 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A45E503409C9530E1F5D12C293F4BD67;
			remoteInfo = permission_handler_apple;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0525459B9CFED0A3CCFDFEA1A6947B97 /* FlutterQrPlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FlutterQrPlugin.h; path = "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/FlutterQrPlugin.h"; sourceTree = "<group>"; };
		0652FCE3FC19056983AABE058B3CC45B /* shared_preferences_foundation-shared_preferences_foundation_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "shared_preferences_foundation-shared_preferences_foundation_privacy"; path = shared_preferences_foundation_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		070ED2FD37AF408D3C19A14BF90E80BF /* Flutter.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Flutter.release.xcconfig; sourceTree = "<group>"; };
		092D9F017D692F3C5C9136112F1511BE /* BackgroundRefreshStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = BackgroundRefreshStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.h"; sourceTree = "<group>"; };
		0AC70C1EDC054FACE700CD1494E02AC1 /* AppTrackingTransparencyPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AppTrackingTransparencyPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.h"; sourceTree = "<group>"; };
		0B7F1FF61B52EFA422A441B6E914F5C3 /* NotificationPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = NotificationPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.h"; sourceTree = "<group>"; };
		0D351F23C38290B0C3B49DC7310E8B45 /* Pods-Runner.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-Runner.modulemap"; sourceTree = "<group>"; };
		0DAEA1B3E8A678EDFF568418AB5715E4 /* qr_code_scanner.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = qr_code_scanner.podspec; path = "../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/qr_code_scanner.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		0F00C6E6905214EA9528DB75D44482EA /* MediaLibraryPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MediaLibraryPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.m"; sourceTree = "<group>"; };
		112E0388B50192EC223BE1368956BB05 /* AssistantPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AssistantPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.h"; sourceTree = "<group>"; };
		118FC883DD11915A680D670B63DC03AB /* Flutter.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; path = Flutter.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		12E8299A93E28906F0E519527728B158 /* messages.g.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = messages.g.swift; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift"; sourceTree = "<group>"; };
		15F2D3E02FE547A64D2E13B9E37EEF5D /* shared_preferences_foundation.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = shared_preferences_foundation.modulemap; sourceTree = "<group>"; };
		16754451CC068C973247E72B5EACF78C /* Pods-Runner-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-Runner-Info.plist"; sourceTree = "<group>"; };
		16F60600D7EFA8B40161C84E63220F4A /* QRViewFactory.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = QRViewFactory.swift; path = "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/QRViewFactory.swift"; sourceTree = "<group>"; };
		174B2B4C37A9BBA84A7BEA7C9EE10BE3 /* permission_handler_apple.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = permission_handler_apple.podspec; path = "../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/permission_handler_apple.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		19942BB0CC239C074112446425810856 /* MTBBarcodeScanner.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MTBBarcodeScanner.m; path = Classes/ios/Scanners/MTBBarcodeScanner.m; sourceTree = "<group>"; };
		1ADB61306F50F60E65BED44878011822 /* Pods-RunnerTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-RunnerTests.release.xcconfig"; sourceTree = "<group>"; };
		1CFCE387C749144A9CDF65CFF5188BCE /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.4/ios/url_launcher_ios/Sources/url_launcher_ios/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		1FC96341216BBB5BBE3744FB1F35DEEE /* Pods-RunnerTests.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-RunnerTests.profile.xcconfig"; sourceTree = "<group>"; };
		21639B22B7A4817A079DFA0186A4C856 /* StoragePermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = StoragePermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.h"; sourceTree = "<group>"; };
		245889C4664F78A1A205C821E81E4C69 /* PhonePermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = PhonePermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.h"; sourceTree = "<group>"; };
		2804FD97F1F9DF9DF72E5E1C6EC026C6 /* SensorPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SensorPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.m"; sourceTree = "<group>"; };
		2A33CC8B0E1420C2EB5D6244E6C86311 /* url_launcher_ios.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = url_launcher_ios.podspec; path = "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.4/ios/url_launcher_ios.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		2AD35495F0539645B05298768B095719 /* MTBBarcodeScanner */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = MTBBarcodeScanner; path = MTBBarcodeScanner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2B3AF9D90D4B088422687FFF4641CBC3 /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		2BDE063589FFFBD7DB5F3F8FB164E042 /* url_launcher_ios.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = url_launcher_ios.debug.xcconfig; sourceTree = "<group>"; };
		2C0BAD9CD71DD8377EB97F64A55F7255 /* messages.g.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = messages.g.swift; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.4/ios/url_launcher_ios/Sources/url_launcher_ios/messages.g.swift"; sourceTree = "<group>"; };
		2CB04EB554430E41E5A19EDE6D62C9F2 /* Pods-RunnerTests-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-RunnerTests-umbrella.h"; sourceTree = "<group>"; };
		2D1B2FEA76ABA898F1AE217811488F59 /* MTBBarcodeScanner-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "MTBBarcodeScanner-Info.plist"; sourceTree = "<group>"; };
		2D37A765B5BB843672F488B32BC47EBA /* BluetoothPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = BluetoothPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.m"; sourceTree = "<group>"; };
		2F0908B8A5026151E2800777E4B17F20 /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		2FEF602FB14C96EEA81E5E79F87909FF /* BluetoothPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = BluetoothPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.h"; sourceTree = "<group>"; };
		30EB37C2078C48C1E0A6D32CCD23A84F /* PhotoPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = PhotoPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.m"; sourceTree = "<group>"; };
		3448DBF71902BD6910D8FEF896BD424D /* ContactPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = ContactPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.m"; sourceTree = "<group>"; };
		355D0180080F026E55C27BAC37B41ABA /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVFoundation.framework; sourceTree = DEVELOPER_DIR; };
		3777446D4E67DF764C1751AC50D6BF3C /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE"; sourceTree = "<group>"; };
		3E0C6DB2269FA4E66744A996E387AC80 /* StoragePermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = StoragePermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.m"; sourceTree = "<group>"; };
		3E249AAAE8E92B5750DB3E8ADB534D8F /* Pods-RunnerTests-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-RunnerTests-acknowledgements.plist"; sourceTree = "<group>"; };
		42D244B224C323C72F50E471E7EDAA41 /* CriticalAlertsPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = CriticalAlertsPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.m"; sourceTree = "<group>"; };
		4342F2FD6C28B7884D6544FF56C54D44 /* shared_preferences_foundation.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = shared_preferences_foundation.release.xcconfig; sourceTree = "<group>"; };
		45E120F4A9F7D13802BA4D4E76E24EFC /* Pods-Runner-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-Runner-acknowledgements.plist"; sourceTree = "<group>"; };
		464DF6186CDB82F6DE9DF13FE47FDD60 /* permission_handler_apple-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "permission_handler_apple-Info.plist"; sourceTree = "<group>"; };
		4683C6A6BA60720F17CAD71C28988BDC /* permission_handler_apple-permission_handler_apple_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "permission_handler_apple-permission_handler_apple_privacy"; path = permission_handler_apple_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		4782A09789918143CBF20C950BBD52BC /* ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist"; sourceTree = "<group>"; };
		4C3698AA06F487DB27EBF02A2BE1220B /* MTBBarcodeScanner-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "MTBBarcodeScanner-umbrella.h"; sourceTree = "<group>"; };
		4CB131F3BC1D6D2C79680B59CF30EF6B /* Pods-Runner-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-Runner-dummy.m"; sourceTree = "<group>"; };
		4F84E419D89D3C6FBDE57D7E887FD6F0 /* shared_preferences_foundation-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "shared_preferences_foundation-prefix.pch"; sourceTree = "<group>"; };
		530C6C1A6F1E8056EBCED03F3E864F05 /* EventPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = EventPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.m"; sourceTree = "<group>"; };
		5568ABAE5F46EBB3C8E48B9E0C5D48BD /* LocationPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = LocationPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.h"; sourceTree = "<group>"; };
		56F49ABAEE03DCC38F0D92B0CD9E9827 /* PermissionManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = PermissionManager.m; path = "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.m"; sourceTree = "<group>"; };
		571ED7C468597046CF74AC12E0C58EDC /* permission_handler_apple.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = permission_handler_apple.debug.xcconfig; sourceTree = "<group>"; };
		5AA99A0261622A434365187FD8F519EF /* CriticalAlertsPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = CriticalAlertsPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.h"; sourceTree = "<group>"; };
		5C0BDF52FF6FECE67F727FCB526D6302 /* ContactPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ContactPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.h"; sourceTree = "<group>"; };
		5C12E65822F20EA0FE40CCA3FD20E664 /* url_launcher_ios-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "url_launcher_ios-dummy.m"; sourceTree = "<group>"; };
		61837375FA2BCB14F68D85EBFF0454E4 /* NotificationPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = NotificationPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.m"; sourceTree = "<group>"; };
		65F7A10D6F94FD6BDC89FAFD865C8FFF /* PermissionHandlerPlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = PermissionHandlerPlugin.h; path = "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.h"; sourceTree = "<group>"; };
		65FFD225084FCE50ADEFD7A3E8A4D984 /* MTBBarcodeScanner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = MTBBarcodeScanner.release.xcconfig; sourceTree = "<group>"; };
		6650424E8801DD80E8081791DCEBC4A0 /* ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist"; sourceTree = "<group>"; };
		665227A32ADA8E64F557EE07C846FB37 /* SpeechPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SpeechPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.h"; sourceTree = "<group>"; };
		669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-Runner"; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		676883D38B17DD3EAF2928CFA719C0A4 /* url_launcher_ios.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = url_launcher_ios.modulemap; sourceTree = "<group>"; };
		6C3345B1B3CAEDF5B03B1F731FDC492E /* Pods-RunnerTests */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-RunnerTests"; path = Pods_RunnerTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		74516BEF7E27FA4A7E26ECD68F16B9F9 /* qr_code_scanner-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "qr_code_scanner-prefix.pch"; sourceTree = "<group>"; };
		75110DCFA099106763E672B13F723E84 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE"; sourceTree = "<group>"; };
		773A231F8701E8A75B979E34900CDD39 /* PermissionHandlerPlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = PermissionHandlerPlugin.m; path = "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.m"; sourceTree = "<group>"; };
		78B952E3264769CE1C28EB4C6EAE9446 /* shared_preferences_foundation-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "shared_preferences_foundation-umbrella.h"; sourceTree = "<group>"; };
		790F207D667079AB3ACEE730C8D9A74C /* PermissionManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = PermissionManager.h; path = "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.h"; sourceTree = "<group>"; };
		797BED1744CA9A7D48FB7F71CD91FFD5 /* Flutter.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Flutter.debug.xcconfig; sourceTree = "<group>"; };
		7AD6D0F6201BC8F67087A69CB20CD8CE /* qr_code_scanner-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "qr_code_scanner-dummy.m"; sourceTree = "<group>"; };
		7B7C06D35B3BC2BD649AAA1A489E49DA /* url_launcher_ios */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = url_launcher_ios; path = url_launcher_ios.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		7E2EF37CBC80FE81454E2B92519309C1 /* qr_code_scanner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = qr_code_scanner.debug.xcconfig; sourceTree = "<group>"; };
		7EE849350B031F9293FF39B2BE018644 /* BackgroundRefreshStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = BackgroundRefreshStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.m"; sourceTree = "<group>"; };
		7FA1EE3150AB7E2D7A76E646E7CA6C1E /* Pods-Runner-resources.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-Runner-resources.sh"; sourceTree = "<group>"; };
		817671F15D4B947F617BD4EE8ACFC278 /* Pods-RunnerTests-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-RunnerTests-Info.plist"; sourceTree = "<group>"; };
		8233F69850A620ECE83BC1D740C5BFD8 /* qr_code_scanner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = qr_code_scanner.release.xcconfig; sourceTree = "<group>"; };
		84E9D0EE8F2A99809C836DFF931BBE7A /* qr_code_scanner.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = qr_code_scanner.modulemap; sourceTree = "<group>"; };
		8522A9F0207B1F17F62C115934CFC03E /* url_launcher_ios-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "url_launcher_ios-prefix.pch"; sourceTree = "<group>"; };
		85EAF159AABB5D96C3B51BA3D8EF8FF7 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		8758A56177F57A2EE30894AA6F81B31A /* Pods-RunnerTests-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-RunnerTests-dummy.m"; sourceTree = "<group>"; };
		8A432929F7E93D3E1AA06A4A8668C699 /* Pods-RunnerTests-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-RunnerTests-acknowledgements.markdown"; sourceTree = "<group>"; };
		8BB0FC92B6D33B68FFB49CA3ABB60EB0 /* AppTrackingTransparencyPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AppTrackingTransparencyPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.m"; sourceTree = "<group>"; };
		8EDC0AE1F9076AD2F7A1FDCD374A002C /* qr_code_scanner */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = qr_code_scanner; path = qr_code_scanner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		906C5F1E88D3383DA6E6FFE986454BB4 /* MTBBarcodeScanner-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "MTBBarcodeScanner-dummy.m"; sourceTree = "<group>"; };
		913E74A677D574BA78CE2BC58164FAD8 /* SwiftFlutterQrPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SwiftFlutterQrPlugin.swift; path = "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/SwiftFlutterQrPlugin.swift"; sourceTree = "<group>"; };
		93841520DF715A82BA54F0BFD9759C53 /* Launcher.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Launcher.swift; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.4/ios/url_launcher_ios/Sources/url_launcher_ios/Launcher.swift"; sourceTree = "<group>"; };
		93C6A45C6E4792269BE9BE0073839BF0 /* shared_preferences_foundation */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = shared_preferences_foundation; path = shared_preferences_foundation.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		93C96FC37DD0F2078926E0C08A7D12E7 /* url_launcher_ios.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = url_launcher_ios.release.xcconfig; sourceTree = "<group>"; };
		949100FEE0EC46F03CAB377A7576626A /* permission_handler_apple-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "permission_handler_apple-dummy.m"; sourceTree = "<group>"; };
		95CF325965B1D945D2698FA2DDEF7B90 /* ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist"; sourceTree = "<group>"; };
		980A6251B72ADDBEAC19D8BA314E3C54 /* QRView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = QRView.swift; path = "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/QRView.swift"; sourceTree = "<group>"; };
		9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		A2F40AF64DB695248267036C3596680A /* permission_handler_apple-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "permission_handler_apple-prefix.pch"; sourceTree = "<group>"; };
		A586C747E3965EC542D2E8F470FC7721 /* PermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = PermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PermissionStrategy.h"; sourceTree = "<group>"; };
		A8FDBAA5DB30273D3A49EC23FF64001E /* Pods-Runner-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-Runner-acknowledgements.markdown"; sourceTree = "<group>"; };
		AFE0F90CFC7BE623CE6BC48F588017BA /* permission_handler_apple.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = permission_handler_apple.release.xcconfig; sourceTree = "<group>"; };
		B30388C1FF31B003DDC05B7BBD64447C /* MediaLibraryPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MediaLibraryPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.h"; sourceTree = "<group>"; };
		B40DF1B6ECCDD2D2E8AF7959C62E8EEC /* shared_preferences_foundation.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = shared_preferences_foundation.podspec; path = "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		B62A811C47C616E581AC9F81815A04DE /* SpeechPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SpeechPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.m"; sourceTree = "<group>"; };
		B954F384544AEE9A82B8000480F7C8EC /* PhonePermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = PhonePermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.m"; sourceTree = "<group>"; };
		BCC84BDE7260B712B097666E169A193C /* Pods-Runner-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-Runner-umbrella.h"; sourceTree = "<group>"; };
		BD2E818BCFC28AFE22EF49432CBF49BE /* shared_preferences_foundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "shared_preferences_foundation-Info.plist"; sourceTree = "<group>"; };
		BE05F9BC7EF24B8C7CE0744D0D039925 /* Codec.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = Codec.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/util/Codec.h"; sourceTree = "<group>"; };
		BEDCBCDEBBDDBBA83C0499B70E168D7B /* url_launcher_ios-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "url_launcher_ios-Info.plist"; sourceTree = "<group>"; };
		BEFCB86F79AB622B3CF0458603A15B9B /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.4/LICENSE"; sourceTree = "<group>"; };
		C1F63004BE8D5A35263B6FB70141E104 /* LocationPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = LocationPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.m"; sourceTree = "<group>"; };
		C3E607D4244AD6D9ED532E69A6DA10DD /* MTBBarcodeScanner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = MTBBarcodeScanner.debug.xcconfig; sourceTree = "<group>"; };
		C588489D32FDC33C42C63BB8A3A3E2A4 /* AudioVideoPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AudioVideoPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.h"; sourceTree = "<group>"; };
		C61AAF706076321407BE0DA7A1AAC994 /* shared_preferences_foundation.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = shared_preferences_foundation.debug.xcconfig; sourceTree = "<group>"; };
		C7AD28D5FB25A8DEDF61F78996932FA6 /* permission_handler_apple */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = permission_handler_apple; path = permission_handler_apple.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		CB5CCAD432EFEE2ACB006BC943F241DB /* SensorPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SensorPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.h"; sourceTree = "<group>"; };
		CBF60097BD84F1E2284D5E2AFC8EA642 /* AssistantPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AssistantPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.m"; sourceTree = "<group>"; };
		CCDC8DDDB535F43BEF6DC5BCBE42FC8B /* qr_code_scanner-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "qr_code_scanner-Info.plist"; sourceTree = "<group>"; };
		CE80AEE54213307F43286027D20CA42C /* MTBBarcodeScanner.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTBBarcodeScanner.h; path = Classes/ios/Scanners/MTBBarcodeScanner.h; sourceTree = "<group>"; };
		CFA56F7544C7A03823E1D2D749934BFC /* Pods-Runner-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-Runner-frameworks.sh"; sourceTree = "<group>"; };
		D0E21B5259420515F976F0832205CC02 /* Codec.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = Codec.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/util/Codec.m"; sourceTree = "<group>"; };
		D1670C95B945B1C630A9879C3BCE9E47 /* MTBBarcodeScanner-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "MTBBarcodeScanner-prefix.pch"; sourceTree = "<group>"; };
		D50C565374CC523CF6B46DEFCD73D5F0 /* permission_handler_apple-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "permission_handler_apple-umbrella.h"; sourceTree = "<group>"; };
		D537A5893A88B49628C1AA1DFCD97964 /* qr_code_scanner-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "qr_code_scanner-umbrella.h"; sourceTree = "<group>"; };
		D826AB59FE76AF44322D97703DD0590F /* UnknownPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = UnknownPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.h"; sourceTree = "<group>"; };
		DBA6F34C072B134D3BE38983776DD1C3 /* url_launcher_ios-url_launcher_ios_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "url_launcher_ios-url_launcher_ios_privacy"; path = url_launcher_ios_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		DD8C8ACC853AF1B657D17AEC50E540A9 /* Pods-RunnerTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-RunnerTests.debug.xcconfig"; sourceTree = "<group>"; };
		DEFE8DAFF33836AE453B8A4D3EB4B4CB /* SharedPreferencesPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SharedPreferencesPlugin.swift; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift"; sourceTree = "<group>"; };
		E370E293DF26460F4DAFC2FB9FDB18B9 /* permission_handler_apple.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = permission_handler_apple.modulemap; sourceTree = "<group>"; };
		E633951C848CF6F29BEFEFF26F002EEF /* PermissionHandlerEnums.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = PermissionHandlerEnums.h; path = "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerEnums.h"; sourceTree = "<group>"; };
		E6953E238C0C1CB99C3CDC2ACC7580EF /* FlutterQrPlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FlutterQrPlugin.m; path = "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/FlutterQrPlugin.m"; sourceTree = "<group>"; };
		E9C5F640AD2476216F4F542B0AF5E13C /* Pods-RunnerTests.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-RunnerTests.modulemap"; sourceTree = "<group>"; };
		ED9AE874B2ED0ADD8CCA4E36CF53A7C7 /* AudioVideoPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AudioVideoPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.m"; sourceTree = "<group>"; };
		EE84B896A27D8CAB0349A3768452DB14 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		EE927CD5B46DAB5884E10036C11F7798 /* shared_preferences_foundation-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "shared_preferences_foundation-dummy.m"; sourceTree = "<group>"; };
		EECBB7DFB0941CA8E5664F05BDC38314 /* url_launcher_ios-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "url_launcher_ios-umbrella.h"; sourceTree = "<group>"; };
		F208A918CBD039E4F15809E82A10CFC6 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/LICENSE"; sourceTree = "<group>"; };
		F50ED5DF2A1A9902D41E6CD0644D7327 /* UnknownPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = UnknownPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.m"; sourceTree = "<group>"; };
		F5449C617B01870C6A67A69BE87631A5 /* URLLauncherPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = URLLauncherPlugin.swift; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.4/ios/url_launcher_ios/Sources/url_launcher_ios/URLLauncherPlugin.swift"; sourceTree = "<group>"; };
		F682705073E974C61A51F88C202D15CC /* URLLaunchSession.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = URLLaunchSession.swift; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.4/ios/url_launcher_ios/Sources/url_launcher_ios/URLLaunchSession.swift"; sourceTree = "<group>"; };
		F74A98ABA8B82A390D306C589A96545D /* EventPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = EventPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.h"; sourceTree = "<group>"; };
		F8547457089967DAC30C3130D4EDF7D1 /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		F94B295CD57777CC6E09C8343656D6B1 /* MTBBarcodeScanner.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = MTBBarcodeScanner.modulemap; sourceTree = "<group>"; };
		F9E5AF19473BBB6D6B21A3B29833FDE0 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/QuartzCore.framework; sourceTree = DEVELOPER_DIR; };
		FFAF7031D64D5046D86C2F180D2E308B /* PhotoPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = PhotoPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.h"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		2B7D7E5068BBE5986E6E46858588B9B1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				03432570AB88998A035398D46B01F04C /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2EF5CBD48B25E094D991FEACD9B3F97B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BC2C168999E585DBDF5CDD8E9E5217DD /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		413B37DCF7BA9F02D333644BD78D0235 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9F3376F43893107B68E2FB140B5A2268 /* AVFoundation.framework in Frameworks */,
				12B38331E4F92C9745F0FF0F3FE8C1B6 /* Foundation.framework in Frameworks */,
				DD54438BFB24943B91DBDD11A58D0A03 /* QuartzCore.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4380924F566AA01EB048DC15F9BC6D33 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEBD84922D2CCEF26C272418EC3EB3A6 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		607B5F60EE6A367D772241DAE3CE06CE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		68ECC876D912C3785FE2C45A2AA757F7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		712FC71A4B241A4AD945DB38DDF4EA28 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C14990D6EA8F277C8CB3FBBF8E2784EC /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9747146C330459A9D0D5A5EFFD012568 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9B9CEE0D664C57FB31CCAA70D76A9E87 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A28030D2BFE1E8809475BF462C5A1050 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C60368AE5AB595A6FB47AB86BB81F10B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E6B934071CC8AAC4C2F13418E980DC83 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00172C6DDB227D9F28FE1CF7FF31FC50 /* url_launcher_ios */ = {
			isa = PBXGroup;
			children = (
				93841520DF715A82BA54F0BFD9759C53 /* Launcher.swift */,
				2C0BAD9CD71DD8377EB97F64A55F7255 /* messages.g.swift */,
				F5449C617B01870C6A67A69BE87631A5 /* URLLauncherPlugin.swift */,
				F682705073E974C61A51F88C202D15CC /* URLLaunchSession.swift */,
			);
			name = url_launcher_ios;
			path = url_launcher_ios;
			sourceTree = "<group>";
		};
		03C5C200A0787E300053CFA8F53CA094 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				B649CEAE405D60AA4AD30C1AAE59C467 /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		04B94E3E1F8428A3BE8567B6AFFAF8AA /* darwin */ = {
			isa = PBXGroup;
			children = (
				EE06C6A97D0BC852E6BE5A1F26B4E0E4 /* shared_preferences_foundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		060393AD02EF71E60CA577EC6C941E61 /* url_launcher_ios */ = {
			isa = PBXGroup;
			children = (
				E4A3A7E6920EF6FBC8BCFEA097941D8E /* Sources */,
			);
			name = url_launcher_ios;
			path = url_launcher_ios;
			sourceTree = "<group>";
		};
		09A3EC0D3B7E680E99D9CEB8ECAC67DB /* strategies */ = {
			isa = PBXGroup;
			children = (
				0AC70C1EDC054FACE700CD1494E02AC1 /* AppTrackingTransparencyPermissionStrategy.h */,
				8BB0FC92B6D33B68FFB49CA3ABB60EB0 /* AppTrackingTransparencyPermissionStrategy.m */,
				112E0388B50192EC223BE1368956BB05 /* AssistantPermissionStrategy.h */,
				CBF60097BD84F1E2284D5E2AFC8EA642 /* AssistantPermissionStrategy.m */,
				C588489D32FDC33C42C63BB8A3A3E2A4 /* AudioVideoPermissionStrategy.h */,
				ED9AE874B2ED0ADD8CCA4E36CF53A7C7 /* AudioVideoPermissionStrategy.m */,
				092D9F017D692F3C5C9136112F1511BE /* BackgroundRefreshStrategy.h */,
				7EE849350B031F9293FF39B2BE018644 /* BackgroundRefreshStrategy.m */,
				2FEF602FB14C96EEA81E5E79F87909FF /* BluetoothPermissionStrategy.h */,
				2D37A765B5BB843672F488B32BC47EBA /* BluetoothPermissionStrategy.m */,
				5C0BDF52FF6FECE67F727FCB526D6302 /* ContactPermissionStrategy.h */,
				3448DBF71902BD6910D8FEF896BD424D /* ContactPermissionStrategy.m */,
				5AA99A0261622A434365187FD8F519EF /* CriticalAlertsPermissionStrategy.h */,
				42D244B224C323C72F50E471E7EDAA41 /* CriticalAlertsPermissionStrategy.m */,
				F74A98ABA8B82A390D306C589A96545D /* EventPermissionStrategy.h */,
				530C6C1A6F1E8056EBCED03F3E864F05 /* EventPermissionStrategy.m */,
				5568ABAE5F46EBB3C8E48B9E0C5D48BD /* LocationPermissionStrategy.h */,
				C1F63004BE8D5A35263B6FB70141E104 /* LocationPermissionStrategy.m */,
				B30388C1FF31B003DDC05B7BBD64447C /* MediaLibraryPermissionStrategy.h */,
				0F00C6E6905214EA9528DB75D44482EA /* MediaLibraryPermissionStrategy.m */,
				0B7F1FF61B52EFA422A441B6E914F5C3 /* NotificationPermissionStrategy.h */,
				61837375FA2BCB14F68D85EBFF0454E4 /* NotificationPermissionStrategy.m */,
				A586C747E3965EC542D2E8F470FC7721 /* PermissionStrategy.h */,
				245889C4664F78A1A205C821E81E4C69 /* PhonePermissionStrategy.h */,
				B954F384544AEE9A82B8000480F7C8EC /* PhonePermissionStrategy.m */,
				FFAF7031D64D5046D86C2F180D2E308B /* PhotoPermissionStrategy.h */,
				30EB37C2078C48C1E0A6D32CCD23A84F /* PhotoPermissionStrategy.m */,
				CB5CCAD432EFEE2ACB006BC943F241DB /* SensorPermissionStrategy.h */,
				2804FD97F1F9DF9DF72E5E1C6EC026C6 /* SensorPermissionStrategy.m */,
				665227A32ADA8E64F557EE07C846FB37 /* SpeechPermissionStrategy.h */,
				B62A811C47C616E581AC9F81815A04DE /* SpeechPermissionStrategy.m */,
				21639B22B7A4817A079DFA0186A4C856 /* StoragePermissionStrategy.h */,
				3E0C6DB2269FA4E66744A996E387AC80 /* StoragePermissionStrategy.m */,
				D826AB59FE76AF44322D97703DD0590F /* UnknownPermissionStrategy.h */,
				F50ED5DF2A1A9902D41E6CD0644D7327 /* UnknownPermissionStrategy.m */,
			);
			name = strategies;
			path = strategies;
			sourceTree = "<group>";
		};
		0AA33E02ED8DE2A7C94B0DFD9D326F49 /* url_launcher_ios */ = {
			isa = PBXGroup;
			children = (
				6F0B0BAD673449448291FDA6BE0B96FB /* .. */,
				AD569C9CE0D201B78940E8B20F2C8220 /* Pod */,
				24CB43E8ABE0AE5E9C44A5E1F459DA68 /* Support Files */,
			);
			name = url_launcher_ios;
			path = ../.symlinks/plugins/url_launcher_ios/ios;
			sourceTree = "<group>";
		};
		0AC88C2D5D1E32D4F0F514E0593E7535 /* qrcode_safe */ = {
			isa = PBXGroup;
			children = (
				F18A1253BCB66509249E96F6659A7972 /* ios */,
			);
			name = qrcode_safe;
			path = qrcode_safe;
			sourceTree = "<group>";
		};
		0AFC45C2ACF82CD68026EF18B083E136 /* plugins */ = {
			isa = PBXGroup;
			children = (
				54910DDA7DA9FEFA4D0DA58235CDDE3F /* shared_preferences_foundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		0C3BD55432E15A6CDA698B35CFCB92AB /* .. */ = {
			isa = PBXGroup;
			children = (
				FC44DB74365CEEAAB3428856DD1A6D53 /* .. */,
				B0399EAA155E5B9736BB5356A71D9E9D /* qrcode-safe */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		129542EC392104B401EA0D2C0CF89B46 /* .. */ = {
			isa = PBXGroup;
			children = (
				9E78CB2115232F450AD075B42281702B /* .. */,
			);
			name = ..;
			path = "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources";
			sourceTree = "<group>";
		};
		160F36C8EA6179ED199FF8BAA4672625 /* Resources */ = {
			isa = PBXGroup;
			children = (
				1CFCE387C749144A9CDF65CFF5188BCE /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		16AA3B3139DF9B192EBE4E75805EB539 /* url_launcher_ios */ = {
			isa = PBXGroup;
			children = (
				160F36C8EA6179ED199FF8BAA4672625 /* Resources */,
			);
			name = url_launcher_ios;
			path = url_launcher_ios;
			sourceTree = "<group>";
		};
		17B3BE944BC21C9074B34D1F986B5D0F /* .. */ = {
			isa = PBXGroup;
			children = (
				246B423F0C18F8BC17D0F93EA8CFB915 /* qrcode-safe */,
			);
			name = ..;
			path = .;
			sourceTree = "<group>";
		};
		20636934091401CBB21CB77E629AF4C6 /* Pods-RunnerTests */ = {
			isa = PBXGroup;
			children = (
				E9C5F640AD2476216F4F542B0AF5E13C /* Pods-RunnerTests.modulemap */,
				8A432929F7E93D3E1AA06A4A8668C699 /* Pods-RunnerTests-acknowledgements.markdown */,
				3E249AAAE8E92B5750DB3E8ADB534D8F /* Pods-RunnerTests-acknowledgements.plist */,
				8758A56177F57A2EE30894AA6F81B31A /* Pods-RunnerTests-dummy.m */,
				817671F15D4B947F617BD4EE8ACFC278 /* Pods-RunnerTests-Info.plist */,
				2CB04EB554430E41E5A19EDE6D62C9F2 /* Pods-RunnerTests-umbrella.h */,
				DD8C8ACC853AF1B657D17AEC50E540A9 /* Pods-RunnerTests.debug.xcconfig */,
				1FC96341216BBB5BBE3744FB1F35DEEE /* Pods-RunnerTests.profile.xcconfig */,
				1ADB61306F50F60E65BED44878011822 /* Pods-RunnerTests.release.xcconfig */,
			);
			name = "Pods-RunnerTests";
			path = "Target Support Files/Pods-RunnerTests";
			sourceTree = "<group>";
		};
		243E25A9C1A42A97FA733C72812F0A5D /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				04B94E3E1F8428A3BE8567B6AFFAF8AA /* darwin */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		246B423F0C18F8BC17D0F93EA8CFB915 /* qrcode-safe */ = {
			isa = PBXGroup;
			children = (
				D2F828279749FE873BEDACA84EAB8727 /* qrcode_safe */,
			);
			name = "qrcode-safe";
			path = "qrcode-safe";
			sourceTree = "<group>";
		};
		24CB43E8ABE0AE5E9C44A5E1F459DA68 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				4782A09789918143CBF20C950BBD52BC /* ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist */,
				676883D38B17DD3EAF2928CFA719C0A4 /* url_launcher_ios.modulemap */,
				5C12E65822F20EA0FE40CCA3FD20E664 /* url_launcher_ios-dummy.m */,
				BEDCBCDEBBDDBBA83C0499B70E168D7B /* url_launcher_ios-Info.plist */,
				8522A9F0207B1F17F62C115934CFC03E /* url_launcher_ios-prefix.pch */,
				EECBB7DFB0941CA8E5664F05BDC38314 /* url_launcher_ios-umbrella.h */,
				2BDE063589FFFBD7DB5F3F8FB164E042 /* url_launcher_ios.debug.xcconfig */,
				93C96FC37DD0F2078926E0C08A7D12E7 /* url_launcher_ios.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/url_launcher_ios";
			sourceTree = "<group>";
		};
		2516E575BA95B04AFE895682CAABC4DC /* qrcode-safe */ = {
			isa = PBXGroup;
			children = (
				332E0E7B69E73D0C40B861648D96733C /* qrcode_safe */,
			);
			name = "qrcode-safe";
			path = "qrcode-safe";
			sourceTree = "<group>";
		};
		27067D5B3B1A44A497921C33FF890B20 /* Development Pods */ = {
			isa = PBXGroup;
			children = (
				403BB0713CD62D190D2C591456344A49 /* Flutter */,
				CC816C0BC58A0042696DB8C513A537FB /* permission_handler_apple */,
				32042EEE523BC1B73054E4F2EE640EE0 /* qr_code_scanner */,
				4CCE791B14CCD8E7D7FF08EE3E1107C2 /* shared_preferences_foundation */,
				0AA33E02ED8DE2A7C94B0DFD9D326F49 /* url_launcher_ios */,
			);
			name = "Development Pods";
			sourceTree = "<group>";
		};
		283D4945F203525CABF68B1EA7170555 /* url_launcher_ios */ = {
			isa = PBXGroup;
			children = (
				9B81AA50835954938AECFE054300560F /* Sources */,
			);
			name = url_launcher_ios;
			path = url_launcher_ios;
			sourceTree = "<group>";
		};
		2CD56E615D38F8685DD67BE462A601E8 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				E370E293DF26460F4DAFC2FB9FDB18B9 /* permission_handler_apple.modulemap */,
				949100FEE0EC46F03CAB377A7576626A /* permission_handler_apple-dummy.m */,
				464DF6186CDB82F6DE9DF13FE47FDD60 /* permission_handler_apple-Info.plist */,
				A2F40AF64DB695248267036C3596680A /* permission_handler_apple-prefix.pch */,
				D50C565374CC523CF6B46DEFCD73D5F0 /* permission_handler_apple-umbrella.h */,
				571ED7C468597046CF74AC12E0C58EDC /* permission_handler_apple.debug.xcconfig */,
				AFE0F90CFC7BE623CE6BC48F588017BA /* permission_handler_apple.release.xcconfig */,
				95CF325965B1D945D2698FA2DDEF7B90 /* ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/permission_handler_apple";
			sourceTree = "<group>";
		};
		30DB5961A19CA7AD553C533157CE8B29 /* permission_handler_apple */ = {
			isa = PBXGroup;
			children = (
				5A815C261B66ABACE40246E6260F29C8 /* ios */,
			);
			name = permission_handler_apple;
			path = permission_handler_apple;
			sourceTree = "<group>";
		};
		32042EEE523BC1B73054E4F2EE640EE0 /* qr_code_scanner */ = {
			isa = PBXGroup;
			children = (
				4168F7D47CD90D0F58C388E603E224D7 /* .. */,
				4B5DC2EA76256EFB6C858C158CAA8669 /* Pod */,
				DC14D5BB397B5018252DDD038D3C7D6D /* Support Files */,
			);
			name = qr_code_scanner;
			path = ../.symlinks/plugins/qr_code_scanner/ios;
			sourceTree = "<group>";
		};
		3280BDF8E7E23BB3A4CA890C27A3B402 /* darwin */ = {
			isa = PBXGroup;
			children = (
				F1D77EF2A2BD9D2A71F33D6A1F77B31F /* shared_preferences_foundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		332E0E7B69E73D0C40B861648D96733C /* qrcode_safe */ = {
			isa = PBXGroup;
			children = (
				B4254694E9EDB1431DA95104F8D5E954 /* ios */,
			);
			name = qrcode_safe;
			path = qrcode_safe;
			sourceTree = "<group>";
		};
		33B48EB8E0DE66BF20AC2EBE599A13DB /* ios */ = {
			isa = PBXGroup;
			children = (
				8B0524680C180E213BF533FCA7A4B54C /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		340342A18BF15DF10CC7E5E9B1741ADE /* Classes */ = {
			isa = PBXGroup;
			children = (
				0525459B9CFED0A3CCFDFEA1A6947B97 /* FlutterQrPlugin.h */,
				E6953E238C0C1CB99C3CDC2ACC7580EF /* FlutterQrPlugin.m */,
				980A6251B72ADDBEAC19D8BA314E3C54 /* QRView.swift */,
				16F60600D7EFA8B40161C84E63220F4A /* QRViewFactory.swift */,
				913E74A677D574BA78CE2BC58164FAD8 /* SwiftFlutterQrPlugin.swift */,
			);
			name = Classes;
			path = Classes;
			sourceTree = "<group>";
		};
		3447F4CC746C201EB26CD7759C3A4813 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				4E5D80AF98320C13B27FFC6454D6291A /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		386703C799D39779F549E0BF53A3A01B /* .. */ = {
			isa = PBXGroup;
			children = (
				A0F7B7E8D062388687CF975640BFB299 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		39A73067EB03DFBA354B894CDE0D7799 /* Sources */ = {
			isa = PBXGroup;
			children = (
				F2FDD35B6001233E36A25447B8E13258 /* shared_preferences_foundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		3D0D7FE09774E62CE75C6BE8C7B461A8 /* .. */ = {
			isa = PBXGroup;
			children = (
				E1EB028189E3F3FDE4AD3B8480A450EA /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		3F2B03188BC127C6760E92419340A777 /* plugins */ = {
			isa = PBXGroup;
			children = (
				A396DE15ADA3DD994FF84CB640AEFA56 /* url_launcher_ios */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		403BB0713CD62D190D2C591456344A49 /* Flutter */ = {
			isa = PBXGroup;
			children = (
				B48E762F38675A8256878AE08D43B0DB /* Pod */,
				826BDCC20590214BCD5B939FC0921E88 /* Support Files */,
			);
			name = Flutter;
			path = ../Flutter;
			sourceTree = "<group>";
		};
		4168F7D47CD90D0F58C388E603E224D7 /* .. */ = {
			isa = PBXGroup;
			children = (
				BBE50E5D908A9E3D7C9F0CF650EBA717 /* .. */,
			);
			name = ..;
			path = "../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios";
			sourceTree = "<group>";
		};
		447E44FEA2662A2235D6FC522458F0C2 /* .. */ = {
			isa = PBXGroup;
			children = (
				17B3BE944BC21C9074B34D1F986B5D0F /* .. */,
				9AEA799A51583B88F1247DBC0E65B4D7 /* qrcode-safe */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		46F0F5A32F75547D59177767349B0B59 /* util */ = {
			isa = PBXGroup;
			children = (
				BE05F9BC7EF24B8C7CE0744D0D039925 /* Codec.h */,
				D0E21B5259420515F976F0832205CC02 /* Codec.m */,
			);
			name = util;
			path = util;
			sourceTree = "<group>";
		};
		47298E64DC292771AFA15CA267B5317C /* Sources */ = {
			isa = PBXGroup;
			children = (
				9497D27849A140BEBD7DB3D4DD779231 /* shared_preferences_foundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		48784BA740BD93553321C337ADC29C92 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				F733C06013DDD6E5B5E1694C37DDB5BD /* Pods-Runner */,
				20636934091401CBB21CB77E629AF4C6 /* Pods-RunnerTests */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		4B5DC2EA76256EFB6C858C158CAA8669 /* Pod */ = {
			isa = PBXGroup;
			children = (
				F208A918CBD039E4F15809E82A10CFC6 /* LICENSE */,
				0DAEA1B3E8A678EDFF568418AB5715E4 /* qr_code_scanner.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		4B7EE4E522770125E2CB30C8B1A94721 /* ios */ = {
			isa = PBXGroup;
			children = (
				060393AD02EF71E60CA577EC6C941E61 /* url_launcher_ios */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		4CCE791B14CCD8E7D7FF08EE3E1107C2 /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				129542EC392104B401EA0D2C0CF89B46 /* .. */,
				5CB1989C6512CA848F86B8BA0D250787 /* Pod */,
				AB1986A0674710852F05BBC23075E39E /* Support Files */,
			);
			name = shared_preferences_foundation;
			path = ../.symlinks/plugins/shared_preferences_foundation/darwin;
			sourceTree = "<group>";
		};
		4E5D80AF98320C13B27FFC6454D6291A /* plugins */ = {
			isa = PBXGroup;
			children = (
				243E25A9C1A42A97FA733C72812F0A5D /* shared_preferences_foundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		4FC0873C90B50484AB442562D97A83E8 /* Pod */ = {
			isa = PBXGroup;
			children = (
				75110DCFA099106763E672B13F723E84 /* LICENSE */,
				174B2B4C37A9BBA84A7BEA7C9EE10BE3 /* permission_handler_apple.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		54910DDA7DA9FEFA4D0DA58235CDDE3F /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				3280BDF8E7E23BB3A4CA890C27A3B402 /* darwin */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		574C46A9057B89ECCF4CD5C7C047720B /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				EC3D5FDE7C4A70471CD1E81366844E90 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		5A815C261B66ABACE40246E6260F29C8 /* ios */ = {
			isa = PBXGroup;
			children = (
				64AF01CC3F53BCA44B55AEBA737EDBE0 /* Classes */,
				C25B7D5731A0FB77D04483B45AEB3A33 /* Resources */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		5B9C2156DDE4999066FD62511CB7E4F4 /* plugins */ = {
			isa = PBXGroup;
			children = (
				D299AD5A818E2241049CB90206DC7CE9 /* qr_code_scanner */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		5CB1989C6512CA848F86B8BA0D250787 /* Pod */ = {
			isa = PBXGroup;
			children = (
				3777446D4E67DF764C1751AC50D6BF3C /* LICENSE */,
				B40DF1B6ECCDD2D2E8AF7959C62E8EEC /* shared_preferences_foundation.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		5CBBD7AFEA62B4053703FFF7C70A371B /* Resources */ = {
			isa = PBXGroup;
			children = (
				85EAF159AABB5D96C3B51BA3D8EF8FF7 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		5CD03CB00CBC2445B72708A1D13E05C7 /* qrcode_safe */ = {
			isa = PBXGroup;
			children = (
				33B48EB8E0DE66BF20AC2EBE599A13DB /* ios */,
			);
			name = qrcode_safe;
			path = qrcode_safe;
			sourceTree = "<group>";
		};
		5E390A34F302E6A6B34F70AC0ED48F32 /* qrcode-safe */ = {
			isa = PBXGroup;
			children = (
				98DECAF9319BD59216A08E9B802D8E80 /* qrcode_safe */,
			);
			name = "qrcode-safe";
			path = "qrcode-safe";
			sourceTree = "<group>";
		};
		64AF01CC3F53BCA44B55AEBA737EDBE0 /* Classes */ = {
			isa = PBXGroup;
			children = (
				E633951C848CF6F29BEFEFF26F002EEF /* PermissionHandlerEnums.h */,
				65F7A10D6F94FD6BDC89FAFD865C8FFF /* PermissionHandlerPlugin.h */,
				773A231F8701E8A75B979E34900CDD39 /* PermissionHandlerPlugin.m */,
				790F207D667079AB3ACEE730C8D9A74C /* PermissionManager.h */,
				56F49ABAEE03DCC38F0D92B0CD9E9827 /* PermissionManager.m */,
				09A3EC0D3B7E680E99D9CEB8ECAC67DB /* strategies */,
				46F0F5A32F75547D59177767349B0B59 /* util */,
			);
			name = Classes;
			path = Classes;
			sourceTree = "<group>";
		};
		652620CD454BD964273F9C41BAA44B45 /* .. */ = {
			isa = PBXGroup;
			children = (
				BAB95C4522825FEA6DDCDFCC85ABC6A7 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		6F0B0BAD673449448291FDA6BE0B96FB /* .. */ = {
			isa = PBXGroup;
			children = (
				B5C922DFAA463BF33C2DE2279968B497 /* .. */,
			);
			name = ..;
			path = "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.4/ios/url_launcher_ios/Sources";
			sourceTree = "<group>";
		};
		826BDCC20590214BCD5B939FC0921E88 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				797BED1744CA9A7D48FB7F71CD91FFD5 /* Flutter.debug.xcconfig */,
				070ED2FD37AF408D3C19A14BF90E80BF /* Flutter.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Pods/Target Support Files/Flutter";
			sourceTree = "<group>";
		};
		833BBB55D2EE0207A56A82C88AAD3C0F /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				0AFC45C2ACF82CD68026EF18B083E136 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		8B0524680C180E213BF533FCA7A4B54C /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				3F2B03188BC127C6760E92419340A777 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		8F932C2DEDA854CE400FBAB0353E21BA /* .. */ = {
			isa = PBXGroup;
			children = (
				A3EC042980F6AE8339B2B54BEB937F8C /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		92FC5BAECF1E694E2EED643A8E73BC3A /* .. */ = {
			isa = PBXGroup;
			children = (
				C5E3B4B0B41EBDE7D3C12F6B85AE7201 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		9497D27849A140BEBD7DB3D4DD779231 /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				5CBBD7AFEA62B4053703FFF7C70A371B /* Resources */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		961E21615F8F2C7CDBCAFE915418DF69 /* .. */ = {
			isa = PBXGroup;
			children = (
				447E44FEA2662A2235D6FC522458F0C2 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		96A013742D714E34661EB6EED6984C08 /* ios */ = {
			isa = PBXGroup;
			children = (
				283D4945F203525CABF68B1EA7170555 /* url_launcher_ios */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		98DECAF9319BD59216A08E9B802D8E80 /* qrcode_safe */ = {
			isa = PBXGroup;
			children = (
				A523CD63B69E9B520816B7454A194B53 /* ios */,
			);
			name = qrcode_safe;
			path = qrcode_safe;
			sourceTree = "<group>";
		};
		999A6B5E0E86E8B241DD3D486C75C476 /* .. */ = {
			isa = PBXGroup;
			children = (
				5E390A34F302E6A6B34F70AC0ED48F32 /* qrcode-safe */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		9AEA799A51583B88F1247DBC0E65B4D7 /* qrcode-safe */ = {
			isa = PBXGroup;
			children = (
				0AC88C2D5D1E32D4F0F514E0593E7535 /* qrcode_safe */,
			);
			name = "qrcode-safe";
			path = "qrcode-safe";
			sourceTree = "<group>";
		};
		9B81AA50835954938AECFE054300560F /* Sources */ = {
			isa = PBXGroup;
			children = (
				00172C6DDB227D9F28FE1CF7FF31FC50 /* url_launcher_ios */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		9E78CB2115232F450AD075B42281702B /* .. */ = {
			isa = PBXGroup;
			children = (
				652620CD454BD964273F9C41BAA44B45 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		A0F7B7E8D062388687CF975640BFB299 /* .. */ = {
			isa = PBXGroup;
			children = (
				CAE68D6717CE48CB7E1A51918EAA846C /* qrcode-safe */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		A396DE15ADA3DD994FF84CB640AEFA56 /* url_launcher_ios */ = {
			isa = PBXGroup;
			children = (
				96A013742D714E34661EB6EED6984C08 /* ios */,
			);
			name = url_launcher_ios;
			path = url_launcher_ios;
			sourceTree = "<group>";
		};
		A3EC042980F6AE8339B2B54BEB937F8C /* .. */ = {
			isa = PBXGroup;
			children = (
				961E21615F8F2C7CDBCAFE915418DF69 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		A523CD63B69E9B520816B7454A194B53 /* ios */ = {
			isa = PBXGroup;
			children = (
				E4D11FE31A3E23542F575396977BDEF9 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		A5AF152CFF6797697B1846320B6D8C3B /* Support Files */ = {
			isa = PBXGroup;
			children = (
				F94B295CD57777CC6E09C8343656D6B1 /* MTBBarcodeScanner.modulemap */,
				906C5F1E88D3383DA6E6FFE986454BB4 /* MTBBarcodeScanner-dummy.m */,
				2D1B2FEA76ABA898F1AE217811488F59 /* MTBBarcodeScanner-Info.plist */,
				D1670C95B945B1C630A9879C3BCE9E47 /* MTBBarcodeScanner-prefix.pch */,
				4C3698AA06F487DB27EBF02A2BE1220B /* MTBBarcodeScanner-umbrella.h */,
				C3E607D4244AD6D9ED532E69A6DA10DD /* MTBBarcodeScanner.debug.xcconfig */,
				65FFD225084FCE50ADEFD7A3E8A4D984 /* MTBBarcodeScanner.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/MTBBarcodeScanner";
			sourceTree = "<group>";
		};
		AB1986A0674710852F05BBC23075E39E /* Support Files */ = {
			isa = PBXGroup;
			children = (
				6650424E8801DD80E8081791DCEBC4A0 /* ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist */,
				15F2D3E02FE547A64D2E13B9E37EEF5D /* shared_preferences_foundation.modulemap */,
				EE927CD5B46DAB5884E10036C11F7798 /* shared_preferences_foundation-dummy.m */,
				BD2E818BCFC28AFE22EF49432CBF49BE /* shared_preferences_foundation-Info.plist */,
				4F84E419D89D3C6FBDE57D7E887FD6F0 /* shared_preferences_foundation-prefix.pch */,
				78B952E3264769CE1C28EB4C6EAE9446 /* shared_preferences_foundation-umbrella.h */,
				C61AAF706076321407BE0DA7A1AAC994 /* shared_preferences_foundation.debug.xcconfig */,
				4342F2FD6C28B7884D6544FF56C54D44 /* shared_preferences_foundation.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/shared_preferences_foundation";
			sourceTree = "<group>";
		};
		AD569C9CE0D201B78940E8B20F2C8220 /* Pod */ = {
			isa = PBXGroup;
			children = (
				BEFCB86F79AB622B3CF0458603A15B9B /* LICENSE */,
				2A33CC8B0E1420C2EB5D6244E6C86311 /* url_launcher_ios.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		B0399EAA155E5B9736BB5356A71D9E9D /* qrcode-safe */ = {
			isa = PBXGroup;
			children = (
				5CD03CB00CBC2445B72708A1D13E05C7 /* qrcode_safe */,
			);
			name = "qrcode-safe";
			path = "qrcode-safe";
			sourceTree = "<group>";
		};
		B4254694E9EDB1431DA95104F8D5E954 /* ios */ = {
			isa = PBXGroup;
			children = (
				574C46A9057B89ECCF4CD5C7C047720B /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		B48E762F38675A8256878AE08D43B0DB /* Pod */ = {
			isa = PBXGroup;
			children = (
				118FC883DD11915A680D670B63DC03AB /* Flutter.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		B5C922DFAA463BF33C2DE2279968B497 /* .. */ = {
			isa = PBXGroup;
			children = (
				3D0D7FE09774E62CE75C6BE8C7B461A8 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		B649CEAE405D60AA4AD30C1AAE59C467 /* iOS */ = {
			isa = PBXGroup;
			children = (
				355D0180080F026E55C27BAC37B41ABA /* AVFoundation.framework */,
				9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */,
				F9E5AF19473BBB6D6B21A3B29833FDE0 /* QuartzCore.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		B7F42335B7928AC3FA59D7B75A1977CD /* Products */ = {
			isa = PBXGroup;
			children = (
				2AD35495F0539645B05298768B095719 /* MTBBarcodeScanner */,
				C7AD28D5FB25A8DEDF61F78996932FA6 /* permission_handler_apple */,
				4683C6A6BA60720F17CAD71C28988BDC /* permission_handler_apple-permission_handler_apple_privacy */,
				669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */,
				6C3345B1B3CAEDF5B03B1F731FDC492E /* Pods-RunnerTests */,
				8EDC0AE1F9076AD2F7A1FDCD374A002C /* qr_code_scanner */,
				93C6A45C6E4792269BE9BE0073839BF0 /* shared_preferences_foundation */,
				0652FCE3FC19056983AABE058B3CC45B /* shared_preferences_foundation-shared_preferences_foundation_privacy */,
				7B7C06D35B3BC2BD649AAA1A489E49DA /* url_launcher_ios */,
				DBA6F34C072B134D3BE38983776DD1C3 /* url_launcher_ios-url_launcher_ios_privacy */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BAB95C4522825FEA6DDCDFCC85ABC6A7 /* .. */ = {
			isa = PBXGroup;
			children = (
				8F932C2DEDA854CE400FBAB0353E21BA /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		BBE50E5D908A9E3D7C9F0CF650EBA717 /* .. */ = {
			isa = PBXGroup;
			children = (
				C1784DD6AFD451D266D5B2CE9D31414C /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		C07B8E70333515330810FE5563E5BC15 /* .. */ = {
			isa = PBXGroup;
			children = (
				DBC5B7CFB75E054A0100AC6BA85959E2 /* .. */,
			);
			name = ..;
			path = "../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios";
			sourceTree = "<group>";
		};
		C1784DD6AFD451D266D5B2CE9D31414C /* .. */ = {
			isa = PBXGroup;
			children = (
				E031855928610CADB2E7EBF0C1FAD0A2 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		C25B7D5731A0FB77D04483B45AEB3A33 /* Resources */ = {
			isa = PBXGroup;
			children = (
				EE84B896A27D8CAB0349A3768452DB14 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		C5E3B4B0B41EBDE7D3C12F6B85AE7201 /* .. */ = {
			isa = PBXGroup;
			children = (
				386703C799D39779F549E0BF53A3A01B /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		CAE68D6717CE48CB7E1A51918EAA846C /* qrcode-safe */ = {
			isa = PBXGroup;
			children = (
				F5E90A07754CBCE6900A3AFC25ADA4DD /* qrcode_safe */,
			);
			name = "qrcode-safe";
			path = "qrcode-safe";
			sourceTree = "<group>";
		};
		CC816C0BC58A0042696DB8C513A537FB /* permission_handler_apple */ = {
			isa = PBXGroup;
			children = (
				C07B8E70333515330810FE5563E5BC15 /* .. */,
				4FC0873C90B50484AB442562D97A83E8 /* Pod */,
				2CD56E615D38F8685DD67BE462A601E8 /* Support Files */,
			);
			name = permission_handler_apple;
			path = ../.symlinks/plugins/permission_handler_apple/ios;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				27067D5B3B1A44A497921C33FF890B20 /* Development Pods */,
				03C5C200A0787E300053CFA8F53CA094 /* Frameworks */,
				E114EBD91E697317058312CA355665C8 /* Pods */,
				B7F42335B7928AC3FA59D7B75A1977CD /* Products */,
				48784BA740BD93553321C337ADC29C92 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D299AD5A818E2241049CB90206DC7CE9 /* qr_code_scanner */ = {
			isa = PBXGroup;
			children = (
				F366D4447A1F5282F6C5F3E51C1F7CC2 /* ios */,
			);
			name = qr_code_scanner;
			path = qr_code_scanner;
			sourceTree = "<group>";
		};
		D2F828279749FE873BEDACA84EAB8727 /* qrcode_safe */ = {
			isa = PBXGroup;
			children = (
				E7343B7884FFFBAC4F72DFD96263E633 /* ios */,
			);
			name = qrcode_safe;
			path = qrcode_safe;
			sourceTree = "<group>";
		};
		D61655E966DAA34577B76AD5F95E8836 /* ios */ = {
			isa = PBXGroup;
			children = (
				DF6B5F8A96E2DEC06601B7BEB7281C3C /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		D99912C888597162F6CD61AE33B09027 /* plugins */ = {
			isa = PBXGroup;
			children = (
				30DB5961A19CA7AD553C533157CE8B29 /* permission_handler_apple */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		DBC5B7CFB75E054A0100AC6BA85959E2 /* .. */ = {
			isa = PBXGroup;
			children = (
				92FC5BAECF1E694E2EED643A8E73BC3A /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		DC14D5BB397B5018252DDD038D3C7D6D /* Support Files */ = {
			isa = PBXGroup;
			children = (
				84E9D0EE8F2A99809C836DFF931BBE7A /* qr_code_scanner.modulemap */,
				7AD6D0F6201BC8F67087A69CB20CD8CE /* qr_code_scanner-dummy.m */,
				CCDC8DDDB535F43BEF6DC5BCBE42FC8B /* qr_code_scanner-Info.plist */,
				74516BEF7E27FA4A7E26ECD68F16B9F9 /* qr_code_scanner-prefix.pch */,
				D537A5893A88B49628C1AA1DFCD97964 /* qr_code_scanner-umbrella.h */,
				7E2EF37CBC80FE81454E2B92519309C1 /* qr_code_scanner.debug.xcconfig */,
				8233F69850A620ECE83BC1D740C5BFD8 /* qr_code_scanner.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/qr_code_scanner";
			sourceTree = "<group>";
		};
		DF6B5F8A96E2DEC06601B7BEB7281C3C /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				D99912C888597162F6CD61AE33B09027 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		E031855928610CADB2E7EBF0C1FAD0A2 /* .. */ = {
			isa = PBXGroup;
			children = (
				E16FAB1BA1C43D9EEEB931CBF64FD890 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		E06B95DCA0F5256C5F3DC656E0E96BB0 /* MTBBarcodeScanner */ = {
			isa = PBXGroup;
			children = (
				CE80AEE54213307F43286027D20CA42C /* MTBBarcodeScanner.h */,
				19942BB0CC239C074112446425810856 /* MTBBarcodeScanner.m */,
				A5AF152CFF6797697B1846320B6D8C3B /* Support Files */,
			);
			name = MTBBarcodeScanner;
			path = MTBBarcodeScanner;
			sourceTree = "<group>";
		};
		E114EBD91E697317058312CA355665C8 /* Pods */ = {
			isa = PBXGroup;
			children = (
				E06B95DCA0F5256C5F3DC656E0E96BB0 /* MTBBarcodeScanner */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		E16FAB1BA1C43D9EEEB931CBF64FD890 /* .. */ = {
			isa = PBXGroup;
			children = (
				999A6B5E0E86E8B241DD3D486C75C476 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		E1EB028189E3F3FDE4AD3B8480A450EA /* .. */ = {
			isa = PBXGroup;
			children = (
				ECBEF5957AAC50234CB01E369E222FBD /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		E4A3A7E6920EF6FBC8BCFEA097941D8E /* Sources */ = {
			isa = PBXGroup;
			children = (
				16AA3B3139DF9B192EBE4E75805EB539 /* url_launcher_ios */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		E4D11FE31A3E23542F575396977BDEF9 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				5B9C2156DDE4999066FD62511CB7E4F4 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		E7343B7884FFFBAC4F72DFD96263E633 /* ios */ = {
			isa = PBXGroup;
			children = (
				833BBB55D2EE0207A56A82C88AAD3C0F /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		E98DAEC6387753B3AFD481F66BBB4277 /* url_launcher_ios */ = {
			isa = PBXGroup;
			children = (
				4B7EE4E522770125E2CB30C8B1A94721 /* ios */,
			);
			name = url_launcher_ios;
			path = url_launcher_ios;
			sourceTree = "<group>";
		};
		EC126C9A5F61D0A953398FFA2FAFB38E /* .. */ = {
			isa = PBXGroup;
			children = (
				0C3BD55432E15A6CDA698B35CFCB92AB /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		EC3D5FDE7C4A70471CD1E81366844E90 /* plugins */ = {
			isa = PBXGroup;
			children = (
				E98DAEC6387753B3AFD481F66BBB4277 /* url_launcher_ios */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		ECBEF5957AAC50234CB01E369E222FBD /* .. */ = {
			isa = PBXGroup;
			children = (
				ED919800AB417B01842AD183F3091A5C /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		ED919800AB417B01842AD183F3091A5C /* .. */ = {
			isa = PBXGroup;
			children = (
				EC126C9A5F61D0A953398FFA2FAFB38E /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		EE06C6A97D0BC852E6BE5A1F26B4E0E4 /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				39A73067EB03DFBA354B894CDE0D7799 /* Sources */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		F18A1253BCB66509249E96F6659A7972 /* ios */ = {
			isa = PBXGroup;
			children = (
				3447F4CC746C201EB26CD7759C3A4813 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		F1D77EF2A2BD9D2A71F33D6A1F77B31F /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				47298E64DC292771AFA15CA267B5317C /* Sources */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		F2FDD35B6001233E36A25447B8E13258 /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				12E8299A93E28906F0E519527728B158 /* messages.g.swift */,
				DEFE8DAFF33836AE453B8A4D3EB4B4CB /* SharedPreferencesPlugin.swift */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		F366D4447A1F5282F6C5F3E51C1F7CC2 /* ios */ = {
			isa = PBXGroup;
			children = (
				340342A18BF15DF10CC7E5E9B1741ADE /* Classes */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		F5E90A07754CBCE6900A3AFC25ADA4DD /* qrcode_safe */ = {
			isa = PBXGroup;
			children = (
				D61655E966DAA34577B76AD5F95E8836 /* ios */,
			);
			name = qrcode_safe;
			path = qrcode_safe;
			sourceTree = "<group>";
		};
		F733C06013DDD6E5B5E1694C37DDB5BD /* Pods-Runner */ = {
			isa = PBXGroup;
			children = (
				0D351F23C38290B0C3B49DC7310E8B45 /* Pods-Runner.modulemap */,
				A8FDBAA5DB30273D3A49EC23FF64001E /* Pods-Runner-acknowledgements.markdown */,
				45E120F4A9F7D13802BA4D4E76E24EFC /* Pods-Runner-acknowledgements.plist */,
				4CB131F3BC1D6D2C79680B59CF30EF6B /* Pods-Runner-dummy.m */,
				CFA56F7544C7A03823E1D2D749934BFC /* Pods-Runner-frameworks.sh */,
				16754451CC068C973247E72B5EACF78C /* Pods-Runner-Info.plist */,
				7FA1EE3150AB7E2D7A76E646E7CA6C1E /* Pods-Runner-resources.sh */,
				BCC84BDE7260B712B097666E169A193C /* Pods-Runner-umbrella.h */,
				2F0908B8A5026151E2800777E4B17F20 /* Pods-Runner.debug.xcconfig */,
				2B3AF9D90D4B088422687FFF4641CBC3 /* Pods-Runner.profile.xcconfig */,
				F8547457089967DAC30C3130D4EDF7D1 /* Pods-Runner.release.xcconfig */,
			);
			name = "Pods-Runner";
			path = "Target Support Files/Pods-Runner";
			sourceTree = "<group>";
		};
		FC44DB74365CEEAAB3428856DD1A6D53 /* .. */ = {
			isa = PBXGroup;
			children = (
				2516E575BA95B04AFE895682CAABC4DC /* qrcode-safe */,
			);
			name = ..;
			path = .;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		04354693D111E28076CE5350505C5592 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				97F91860804C4FF0F95BBCAEC6D85BAF /* url_launcher_ios-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		39106E8E9E80BFAF40A6A700E34221A8 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				71F3553FFF2641F08CB6EEBEBE0AD73C /* Pods-Runner-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D658CCB2512BD58C633CB8B2095F6C3 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				48ABC9CC212358D168731AEE56729598 /* shared_preferences_foundation-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A2DC3141BD061D09686ED0B88893D020 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				718A4F8C77639F1A12F101A9E2EE5E04 /* MTBBarcodeScanner.h in Headers */,
				309549EA2063A75373A0B3580CF8C424 /* MTBBarcodeScanner-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		ABB1D74E139AC5261B050DCE54387545 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D73FD31018FE053DFECE1ADD9091607F /* AppTrackingTransparencyPermissionStrategy.h in Headers */,
				A985D47C7B6B78C64F0211B9E4AA916D /* AssistantPermissionStrategy.h in Headers */,
				A2500528640D2211715118237B1E2B91 /* AudioVideoPermissionStrategy.h in Headers */,
				EBC219C8C0EB22FE57D8CAFD288BB75E /* BackgroundRefreshStrategy.h in Headers */,
				E1F16FC5464194ABA094471ED06A3373 /* BluetoothPermissionStrategy.h in Headers */,
				121679B8561F25B70F958E1C449754C5 /* Codec.h in Headers */,
				8301C3E2174422AC905818E5DD71CA9F /* ContactPermissionStrategy.h in Headers */,
				0EBF612BD9A9D28065172409F6498539 /* CriticalAlertsPermissionStrategy.h in Headers */,
				46AC153588A74102B5C92C37038834AF /* EventPermissionStrategy.h in Headers */,
				2C8CCA1A7610BA60433377EE91A98CBC /* LocationPermissionStrategy.h in Headers */,
				BF0069CAFABF46710A32A9CC4B9D60FA /* MediaLibraryPermissionStrategy.h in Headers */,
				89B66AD0211DF422E28D4BA0FDFB5182 /* NotificationPermissionStrategy.h in Headers */,
				AB6B59C5DB5C485F5E1C0B3013806CF7 /* permission_handler_apple-umbrella.h in Headers */,
				7C7D84E9240ADF7554BFFEC5DECA4D01 /* PermissionHandlerEnums.h in Headers */,
				0E8149EAEA1794AC30BFB6B95E305546 /* PermissionHandlerPlugin.h in Headers */,
				74AE7AA5E923BC7D1F1CE6FB1AF221A2 /* PermissionManager.h in Headers */,
				F0A225DB0B069780B9046F550CF964B7 /* PermissionStrategy.h in Headers */,
				9B8D0A18B5DD029F59DDC582B877B41F /* PhonePermissionStrategy.h in Headers */,
				26FDC6CCF00A736B47E27A0B325FC8AB /* PhotoPermissionStrategy.h in Headers */,
				8BD319D3AAE00D70F1996967AE54CA82 /* SensorPermissionStrategy.h in Headers */,
				0F21C51CEF0C472E416D78035F9817A6 /* SpeechPermissionStrategy.h in Headers */,
				E1290184B9BE7EF8E2EB64C3C38F39CF /* StoragePermissionStrategy.h in Headers */,
				6C9A77B43CA0635F9E3B987759476CFE /* UnknownPermissionStrategy.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EF19ECA05ADC6C5880AFF7ACCDE433F3 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CC766C2F06095124222B9EB87BEAEBBD /* FlutterQrPlugin.h in Headers */,
				AA2937664B69935CCA0FDA5F26BD6165 /* qr_code_scanner-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FC4DA9BD27A4914E75F803590E4C4B8B /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EB2DC96CDCF638AB89007D2DB0F3119A /* Pods-RunnerTests-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		3232F0C0E7C65B232832393F9ADDD8C3 /* Pods-RunnerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D52099AA1537D0F3745166E1889F6CA3 /* Build configuration list for PBXNativeTarget "Pods-RunnerTests" */;
			buildPhases = (
				FC4DA9BD27A4914E75F803590E4C4B8B /* Headers */,
				001CCB0FEA80CD1C6BF534C6D9C6283A /* Sources */,
				4380924F566AA01EB048DC15F9BC6D33 /* Frameworks */,
				4A765108DEAFDEBF078F71CDDBE3414E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				C36A0F5CB8E9CB0642489B44289A5C8A /* PBXTargetDependency */,
			);
			name = "Pods-RunnerTests";
			productName = Pods_RunnerTests;
			productReference = 6C3345B1B3CAEDF5B03B1F731FDC492E /* Pods-RunnerTests */;
			productType = "com.apple.product-type.framework";
		};
		43BE97C40504244259CF3C1D21E7EDB5 /* url_launcher_ios-url_launcher_ios_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = BE5025EBDC2FF7FE1A66F16C3C0D6445 /* Build configuration list for PBXNativeTarget "url_launcher_ios-url_launcher_ios_privacy" */;
			buildPhases = (
				BC8A418477509D02FCE76B2CB0349ECC /* Sources */,
				607B5F60EE6A367D772241DAE3CE06CE /* Frameworks */,
				9A2037F8DC9A9AA6FF7E6457FC7F8398 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "url_launcher_ios-url_launcher_ios_privacy";
			productName = url_launcher_ios_privacy;
			productReference = DBA6F34C072B134D3BE38983776DD1C3 /* url_launcher_ios-url_launcher_ios_privacy */;
			productType = "com.apple.product-type.bundle";
		};
		517E8BED8B0E6D6FD078BD19B0A13893 /* permission_handler_apple-permission_handler_apple_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E7E25C575961A90446FC59E7F36ACA9E /* Build configuration list for PBXNativeTarget "permission_handler_apple-permission_handler_apple_privacy" */;
			buildPhases = (
				D2ECC775165B7405DFE8AE1C43590FD7 /* Sources */,
				68ECC876D912C3785FE2C45A2AA757F7 /* Frameworks */,
				807BD42EA8034D1A090B001392DEB3D1 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "permission_handler_apple-permission_handler_apple_privacy";
			productName = permission_handler_apple_privacy;
			productReference = 4683C6A6BA60720F17CAD71C28988BDC /* permission_handler_apple-permission_handler_apple_privacy */;
			productType = "com.apple.product-type.bundle";
		};
		8B74B458B450D74B75744B87BD747314 /* Pods-Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DA775E1B0BF0A6C4F0F1AC62D60E4C41 /* Build configuration list for PBXNativeTarget "Pods-Runner" */;
			buildPhases = (
				39106E8E9E80BFAF40A6A700E34221A8 /* Headers */,
				289B269564437A80D9AA0AA24B2D6CB1 /* Sources */,
				712FC71A4B241A4AD945DB38DDF4EA28 /* Frameworks */,
				F5001872FB2BC221DA6F490824FA96BA /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				BCC2F69B2819A3100A075A344A3509B4 /* PBXTargetDependency */,
				0FA784375391B3AB0448165B5BF61DC8 /* PBXTargetDependency */,
				48F06C26ACA056C8DBBF4BFDF3C42A75 /* PBXTargetDependency */,
				823DA9DCAEA8963544D645CEC9A63EA2 /* PBXTargetDependency */,
				B883B2E1DEF9B4D43DF1C0C6BD27A90B /* PBXTargetDependency */,
				8895D43F7ED5BC685CDFA48E7A73DE56 /* PBXTargetDependency */,
			);
			name = "Pods-Runner";
			productName = Pods_Runner;
			productReference = 669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */;
			productType = "com.apple.product-type.framework";
		};
		A45E503409C9530E1F5D12C293F4BD67 /* permission_handler_apple */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 89EFA95D7A42D94E2576D20D9240C679 /* Build configuration list for PBXNativeTarget "permission_handler_apple" */;
			buildPhases = (
				ABB1D74E139AC5261B050DCE54387545 /* Headers */,
				72B84C6503F1E5F4C6B4E8A6808DE491 /* Sources */,
				2EF5CBD48B25E094D991FEACD9B3F97B /* Frameworks */,
				C34E22EB1E5502C9809B1EFD361E4A7C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				082CC12703191346A811ABC771030B85 /* PBXTargetDependency */,
				98F7B9FBD21153F51DA5D05D9C98B238 /* PBXTargetDependency */,
			);
			name = permission_handler_apple;
			productName = permission_handler_apple;
			productReference = C7AD28D5FB25A8DEDF61F78996932FA6 /* permission_handler_apple */;
			productType = "com.apple.product-type.framework";
		};
		AB5EE685B22D01885ADD930538E8DD3C /* shared_preferences_foundation */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 51E44378D0EE45FC26653A27A9669C35 /* Build configuration list for PBXNativeTarget "shared_preferences_foundation" */;
			buildPhases = (
				7D658CCB2512BD58C633CB8B2095F6C3 /* Headers */,
				413B07111961A3E5A9004A9C5FEDBC18 /* Sources */,
				C60368AE5AB595A6FB47AB86BB81F10B /* Frameworks */,
				B0C1F9DA0908C6404D0A7A4254A1F50F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				801EA77DE236F2F23A4038DD75F76E09 /* PBXTargetDependency */,
				8EAAF731F40885DE1F9522E865034C63 /* PBXTargetDependency */,
			);
			name = shared_preferences_foundation;
			productName = shared_preferences_foundation;
			productReference = 93C6A45C6E4792269BE9BE0073839BF0 /* shared_preferences_foundation */;
			productType = "com.apple.product-type.framework";
		};
		B6AF8B7CEAF6321719ABBC7E770624DA /* shared_preferences_foundation-shared_preferences_foundation_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6B41E0EB7046A8350E615CE68E2B3477 /* Build configuration list for PBXNativeTarget "shared_preferences_foundation-shared_preferences_foundation_privacy" */;
			buildPhases = (
				2866BF1DD4620D99C13F4F6ECCC3799E /* Sources */,
				A28030D2BFE1E8809475BF462C5A1050 /* Frameworks */,
				268F9A07F979F1F1B7DC17DC97612206 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "shared_preferences_foundation-shared_preferences_foundation_privacy";
			productName = shared_preferences_foundation_privacy;
			productReference = 0652FCE3FC19056983AABE058B3CC45B /* shared_preferences_foundation-shared_preferences_foundation_privacy */;
			productType = "com.apple.product-type.bundle";
		};
		D83CD42EA57F97A225D24BDC7BD6B6F6 /* MTBBarcodeScanner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 38D3ADF0652CE382589F1F3CE1EEE84D /* Build configuration list for PBXNativeTarget "MTBBarcodeScanner" */;
			buildPhases = (
				A2DC3141BD061D09686ED0B88893D020 /* Headers */,
				66DE37C37EBC0314D5F2100831DB5005 /* Sources */,
				413B37DCF7BA9F02D333644BD78D0235 /* Frameworks */,
				83F962F7A678B1CBC3883F8087B43359 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = MTBBarcodeScanner;
			productName = MTBBarcodeScanner;
			productReference = 2AD35495F0539645B05298768B095719 /* MTBBarcodeScanner */;
			productType = "com.apple.product-type.framework";
		};
		DF45E85925DF410BE416B32171F59C1F /* url_launcher_ios */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A510A753FD3AF1785BCB45107877CB90 /* Build configuration list for PBXNativeTarget "url_launcher_ios" */;
			buildPhases = (
				04354693D111E28076CE5350505C5592 /* Headers */,
				4A9CA9F71DA4F121DDB49A59FA1C644C /* Sources */,
				9747146C330459A9D0D5A5EFFD012568 /* Frameworks */,
				1D6B73C1303F55B339571CA9DD304EEE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				3CDC8BD0683532B6BDD09CB5F58D137A /* PBXTargetDependency */,
				E54A1FC347505907B39E57E54A30FC48 /* PBXTargetDependency */,
			);
			name = url_launcher_ios;
			productName = url_launcher_ios;
			productReference = 7B7C06D35B3BC2BD649AAA1A489E49DA /* url_launcher_ios */;
			productType = "com.apple.product-type.framework";
		};
		E864A9DA72EED9027036A46ABD1822BC /* qr_code_scanner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B86BDF942AE09555F4C85B37CFEAE949 /* Build configuration list for PBXNativeTarget "qr_code_scanner" */;
			buildPhases = (
				EF19ECA05ADC6C5880AFF7ACCDE433F3 /* Headers */,
				F6F77E0476D1D71C9FD36AC4550D74E8 /* Sources */,
				2B7D7E5068BBE5986E6E46858588B9B1 /* Frameworks */,
				D0AC7B5683373C4E1300FC0BC2B88787 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				0869414D06E348B5BA90E57C99739A95 /* PBXTargetDependency */,
				E72C9CC97EDB3D08FBC4AFAA937FD360 /* PBXTargetDependency */,
			);
			name = qr_code_scanner;
			productName = qr_code_scanner;
			productReference = 8EDC0AE1F9076AD2F7A1FDCD374A002C /* qr_code_scanner */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = B7F42335B7928AC3FA59D7B75A1977CD /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */,
				D83CD42EA57F97A225D24BDC7BD6B6F6 /* MTBBarcodeScanner */,
				A45E503409C9530E1F5D12C293F4BD67 /* permission_handler_apple */,
				517E8BED8B0E6D6FD078BD19B0A13893 /* permission_handler_apple-permission_handler_apple_privacy */,
				8B74B458B450D74B75744B87BD747314 /* Pods-Runner */,
				3232F0C0E7C65B232832393F9ADDD8C3 /* Pods-RunnerTests */,
				E864A9DA72EED9027036A46ABD1822BC /* qr_code_scanner */,
				AB5EE685B22D01885ADD930538E8DD3C /* shared_preferences_foundation */,
				B6AF8B7CEAF6321719ABBC7E770624DA /* shared_preferences_foundation-shared_preferences_foundation_privacy */,
				DF45E85925DF410BE416B32171F59C1F /* url_launcher_ios */,
				43BE97C40504244259CF3C1D21E7EDB5 /* url_launcher_ios-url_launcher_ios_privacy */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1D6B73C1303F55B339571CA9DD304EEE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				58FF267CBDAA4BDAA8279F31A9CB3C40 /* url_launcher_ios-url_launcher_ios_privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		268F9A07F979F1F1B7DC17DC97612206 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A20F6A21391880DF157F42E08570FD3E /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4A765108DEAFDEBF078F71CDDBE3414E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		807BD42EA8034D1A090B001392DEB3D1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				793A16F2F261D1392CE8D447268BDD60 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		83F962F7A678B1CBC3883F8087B43359 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9A2037F8DC9A9AA6FF7E6457FC7F8398 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				ADAFD83ACFABC2C9A3A595354E84993B /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B0C1F9DA0908C6404D0A7A4254A1F50F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B7C67D475D63B3D6501863172F8AD35F /* shared_preferences_foundation-shared_preferences_foundation_privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C34E22EB1E5502C9809B1EFD361E4A7C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D0AC7B5683373C4E1300FC0BC2B88787 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F5001872FB2BC221DA6F490824FA96BA /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		001CCB0FEA80CD1C6BF534C6D9C6283A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B375EA128E6579366091BAA390BBDD34 /* Pods-RunnerTests-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2866BF1DD4620D99C13F4F6ECCC3799E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		289B269564437A80D9AA0AA24B2D6CB1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				19B553C8B2E45D962CA48D89000E09DE /* Pods-Runner-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		413B07111961A3E5A9004A9C5FEDBC18 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EBC24A935793317E4E9E956E3A9BF5EA /* messages.g.swift in Sources */,
				D2A52C6979E7137696C04242485D21EC /* shared_preferences_foundation-dummy.m in Sources */,
				EAD247F99E22F9672E6556A00D0B5C03 /* SharedPreferencesPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4A9CA9F71DA4F121DDB49A59FA1C644C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AB97410CE8410A5B2279DDF4C43D7572 /* Launcher.swift in Sources */,
				5B06A55C0BD6EC7C93369250069299CA /* messages.g.swift in Sources */,
				7778411698137CA267B6213857D67549 /* url_launcher_ios-dummy.m in Sources */,
				A7562EAC8F35C4207D6DF1FD21AE7023 /* URLLauncherPlugin.swift in Sources */,
				0683FB6A0EA2EF47F0B322524F95133A /* URLLaunchSession.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		66DE37C37EBC0314D5F2100831DB5005 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				036DB75E39D0F256E8F12DB66249E151 /* MTBBarcodeScanner.m in Sources */,
				CC781030BA3802C6B47807E5ECCF5C69 /* MTBBarcodeScanner-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		72B84C6503F1E5F4C6B4E8A6808DE491 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0AE22FF7663B5976F724157B0B55DD56 /* AppTrackingTransparencyPermissionStrategy.m in Sources */,
				2E585A6D3EB862CB358F4236571AF949 /* AssistantPermissionStrategy.m in Sources */,
				E517EE53DE3F1D6977E08A2040FB8C5D /* AudioVideoPermissionStrategy.m in Sources */,
				BCE0C8639D6AE4A8792736C0ABB32A69 /* BackgroundRefreshStrategy.m in Sources */,
				E9D09D2A0AE66B8FCF53A3B0B6171793 /* BluetoothPermissionStrategy.m in Sources */,
				E702B68F1D9F5FBBF77021A57B6642ED /* Codec.m in Sources */,
				4D4CFC73824D102E82A8F3B0EBF43D39 /* ContactPermissionStrategy.m in Sources */,
				3875191124A81F2B1F777FA0F3B4B732 /* CriticalAlertsPermissionStrategy.m in Sources */,
				5C41E2EB060B6981260C5BBA07FE575C /* EventPermissionStrategy.m in Sources */,
				6F6257A610B1AB690E38F1CF7E9AF3F9 /* LocationPermissionStrategy.m in Sources */,
				75DA17B512459AF9817ED928870095AA /* MediaLibraryPermissionStrategy.m in Sources */,
				A0F8833ED8A6AC53A64E844315D69060 /* NotificationPermissionStrategy.m in Sources */,
				4A43F54128D0FDF0B93C48446CE89B1D /* permission_handler_apple-dummy.m in Sources */,
				FA6596239E85709C1B9C53EB4D6B8A6F /* PermissionHandlerPlugin.m in Sources */,
				0C67EF1B7B35D3378528B60173E29CA9 /* PermissionManager.m in Sources */,
				6AC94655CFA316F8DC44B632AE169181 /* PhonePermissionStrategy.m in Sources */,
				10ED346DDDB313562C91F8E5A7EA19A7 /* PhotoPermissionStrategy.m in Sources */,
				F7DF9728D6CF306F2B6A9A90031BB0A7 /* SensorPermissionStrategy.m in Sources */,
				8A377F9A1E700ACDFE60ECF38E7FBF60 /* SpeechPermissionStrategy.m in Sources */,
				DBC3E1374D6F4F81FC8C6AB91DFD48DB /* StoragePermissionStrategy.m in Sources */,
				4305445D8E6067B09985A36FAF7E1C36 /* UnknownPermissionStrategy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BC8A418477509D02FCE76B2CB0349ECC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D2ECC775165B7405DFE8AE1C43590FD7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F6F77E0476D1D71C9FD36AC4550D74E8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C5316A62CBB753EFB2F8E37B58B73F4E /* FlutterQrPlugin.m in Sources */,
				DF972B5566EB8F594474408D1FEEBE03 /* qr_code_scanner-dummy.m in Sources */,
				A9B4005CE5D708F1BBBD78996E2C897E /* QRView.swift in Sources */,
				7F0345C3434305DFAEF184BBD6641FE1 /* QRViewFactory.swift in Sources */,
				C69611CADFD4A4CBD493FA39DC1E7E66 /* SwiftFlutterQrPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		082CC12703191346A811ABC771030B85 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 593350DEB0F244B69DF14054180EEDF7 /* PBXContainerItemProxy */;
		};
		0869414D06E348B5BA90E57C99739A95 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 7DA7E80A3E73DB4836E3DC524DB64610 /* PBXContainerItemProxy */;
		};
		0FA784375391B3AB0448165B5BF61DC8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = MTBBarcodeScanner;
			target = D83CD42EA57F97A225D24BDC7BD6B6F6 /* MTBBarcodeScanner */;
			targetProxy = 7468773F7B305E832D04EB044D5E8C41 /* PBXContainerItemProxy */;
		};
		3CDC8BD0683532B6BDD09CB5F58D137A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = D26F92AA6B87E3E8F7490E32EF0B83AE /* PBXContainerItemProxy */;
		};
		48F06C26ACA056C8DBBF4BFDF3C42A75 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = permission_handler_apple;
			target = A45E503409C9530E1F5D12C293F4BD67 /* permission_handler_apple */;
			targetProxy = E9761E3456CA363CB38036DFB7BF1774 /* PBXContainerItemProxy */;
		};
		801EA77DE236F2F23A4038DD75F76E09 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = A3B07104131B5940C9172C5859866AAB /* PBXContainerItemProxy */;
		};
		823DA9DCAEA8963544D645CEC9A63EA2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = qr_code_scanner;
			target = E864A9DA72EED9027036A46ABD1822BC /* qr_code_scanner */;
			targetProxy = 8F6F5B32D68AA95554B2903C502D1F94 /* PBXContainerItemProxy */;
		};
		8895D43F7ED5BC685CDFA48E7A73DE56 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = url_launcher_ios;
			target = DF45E85925DF410BE416B32171F59C1F /* url_launcher_ios */;
			targetProxy = 2A7B68A7C970313F3519F2FF3DA9F170 /* PBXContainerItemProxy */;
		};
		8EAAF731F40885DE1F9522E865034C63 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "shared_preferences_foundation-shared_preferences_foundation_privacy";
			target = B6AF8B7CEAF6321719ABBC7E770624DA /* shared_preferences_foundation-shared_preferences_foundation_privacy */;
			targetProxy = 1255552792047A14783EFB2EA9D48CB5 /* PBXContainerItemProxy */;
		};
		98F7B9FBD21153F51DA5D05D9C98B238 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "permission_handler_apple-permission_handler_apple_privacy";
			target = 517E8BED8B0E6D6FD078BD19B0A13893 /* permission_handler_apple-permission_handler_apple_privacy */;
			targetProxy = 1018EEAB85873500F1E56CA62986FF4D /* PBXContainerItemProxy */;
		};
		B883B2E1DEF9B4D43DF1C0C6BD27A90B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = shared_preferences_foundation;
			target = AB5EE685B22D01885ADD930538E8DD3C /* shared_preferences_foundation */;
			targetProxy = 988B4D87DD28277C6E2D693D153A80C3 /* PBXContainerItemProxy */;
		};
		BCC2F69B2819A3100A075A344A3509B4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = E87DE1A24A38706E60E4D8199518207B /* PBXContainerItemProxy */;
		};
		C36A0F5CB8E9CB0642489B44289A5C8A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "Pods-Runner";
			target = 8B74B458B450D74B75744B87BD747314 /* Pods-Runner */;
			targetProxy = 883A379E2595F6FF354E7D26C19F17CA /* PBXContainerItemProxy */;
		};
		E54A1FC347505907B39E57E54A30FC48 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "url_launcher_ios-url_launcher_ios_privacy";
			target = 43BE97C40504244259CF3C1D21E7EDB5 /* url_launcher_ios-url_launcher_ios_privacy */;
			targetProxy = D138AAA7C726DA897B31B10EB8081E24 /* PBXContainerItemProxy */;
		};
		E72C9CC97EDB3D08FBC4AFAA937FD360 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = MTBBarcodeScanner;
			target = D83CD42EA57F97A225D24BDC7BD6B6F6 /* MTBBarcodeScanner */;
			targetProxy = 39D96E40726AFCAF3B3811D5F3F258A3 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		0F1546CA873CE89641848A549409E6E5 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2F0908B8A5026151E2800777E4B17F20 /* Pods-Runner.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		11099184FBFDE376BB34AC1E682C4872 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 571ED7C468597046CF74AC12E0C58EDC /* permission_handler_apple.debug.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = permission_handler_apple;
				PRODUCT_NAME = permission_handler_apple;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		28E7871BB8F4505A2762ACB51A210FF3 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 65FFD225084FCE50ADEFD7A3E8A4D984 /* MTBBarcodeScanner.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/MTBBarcodeScanner/MTBBarcodeScanner-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/MTBBarcodeScanner/MTBBarcodeScanner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/MTBBarcodeScanner/MTBBarcodeScanner.modulemap";
				PRODUCT_MODULE_NAME = MTBBarcodeScanner;
				PRODUCT_NAME = MTBBarcodeScanner;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		2B9E26EAE2CD392AD762421F663075A1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		2E4608C055BE4F4E675FECAEBC4CC2A4 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1ADB61306F50F60E65BED44878011822 /* Pods-RunnerTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		31CF6E7E1B226E53B3D3022B2496D0F8 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4342F2FD6C28B7884D6544FF56C54D44 /* shared_preferences_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = shared_preferences_foundation;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = shared_preferences_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		352238F70108F49434B51CFD9D646B3B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4342F2FD6C28B7884D6544FF56C54D44 /* shared_preferences_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = shared_preferences_foundation;
				PRODUCT_NAME = shared_preferences_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		3CD1AF7F3EB5E4F4EDE309AE4CFC8F28 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2BDE063589FFFBD7DB5F3F8FB164E042 /* url_launcher_ios.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/url_launcher_ios/url_launcher_ios-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/url_launcher_ios/url_launcher_ios-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/url_launcher_ios/url_launcher_ios.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = url_launcher_ios;
				PRODUCT_NAME = url_launcher_ios;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		3DD953B17D38F68349BCF4C229F543B6 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C3E607D4244AD6D9ED532E69A6DA10DD /* MTBBarcodeScanner.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/MTBBarcodeScanner/MTBBarcodeScanner-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/MTBBarcodeScanner/MTBBarcodeScanner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/MTBBarcodeScanner/MTBBarcodeScanner.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_MODULE_NAME = MTBBarcodeScanner;
				PRODUCT_NAME = MTBBarcodeScanner;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		40D9065855C81C0AF0EA611178091202 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 571ED7C468597046CF74AC12E0C58EDC /* permission_handler_apple.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/permission_handler_apple";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = permission_handler_apple;
				INFOPLIST_FILE = "Target Support Files/permission_handler_apple/ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = permission_handler_apple_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		418BCD7A28FB52D35E0BA822541241C0 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 93C96FC37DD0F2078926E0C08A7D12E7 /* url_launcher_ios.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/url_launcher_ios";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = url_launcher_ios;
				INFOPLIST_FILE = "Target Support Files/url_launcher_ios/ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = url_launcher_ios_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		42EEEF5DB1D4F9E361EC434FC1138672 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2B3AF9D90D4B088422687FFF4641CBC3 /* Pods-Runner.profile.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		46729596D3E861171179E26D7468B25C /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C61AAF706076321407BE0DA7A1AAC994 /* shared_preferences_foundation.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = shared_preferences_foundation;
				PRODUCT_NAME = shared_preferences_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		56B2356650607B22FE5E86C11DC3C5CE /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2BDE063589FFFBD7DB5F3F8FB164E042 /* url_launcher_ios.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/url_launcher_ios";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = url_launcher_ios;
				INFOPLIST_FILE = "Target Support Files/url_launcher_ios/ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = url_launcher_ios_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		5AF1AC88E5F655C93D1E3B5012F6FDC8 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_PROFILE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Profile;
		};
		633338330E8910204C6A22D077F4E759 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AFE0F90CFC7BE623CE6BC48F588017BA /* permission_handler_apple.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/permission_handler_apple";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = permission_handler_apple;
				INFOPLIST_FILE = "Target Support Files/permission_handler_apple/ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = permission_handler_apple_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		63FAF33E1C55B71A5F5A8B3CC8749F99 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		6D92E79F97BBCFD96CEE5C38A6C21349 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 93C96FC37DD0F2078926E0C08A7D12E7 /* url_launcher_ios.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/url_launcher_ios/url_launcher_ios-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/url_launcher_ios/url_launcher_ios-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/url_launcher_ios/url_launcher_ios.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = url_launcher_ios;
				PRODUCT_NAME = url_launcher_ios;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		70F1E3D053D812E3D7E8E74934CC9053 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 797BED1744CA9A7D48FB7F71CD91FFD5 /* Flutter.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				ONLY_ACTIVE_ARCH = NO;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		723189148229824216DB3B5536FF5DC9 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 93C96FC37DD0F2078926E0C08A7D12E7 /* url_launcher_ios.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/url_launcher_ios/url_launcher_ios-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/url_launcher_ios/url_launcher_ios-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/url_launcher_ios/url_launcher_ios.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = url_launcher_ios;
				PRODUCT_NAME = url_launcher_ios;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		768CE43E68DF2B5EF0C94D45F10B98DF /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4342F2FD6C28B7884D6544FF56C54D44 /* shared_preferences_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = shared_preferences_foundation;
				PRODUCT_NAME = shared_preferences_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		76E053E47C7357CFF4EA96DF8337031F /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 070ED2FD37AF408D3C19A14BF90E80BF /* Flutter.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		78FF88BFAC1F034524695055FAA9173A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AFE0F90CFC7BE623CE6BC48F588017BA /* permission_handler_apple.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/permission_handler_apple";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = permission_handler_apple;
				INFOPLIST_FILE = "Target Support Files/permission_handler_apple/ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = permission_handler_apple_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		7BD1AB4DE0EECC079966841AD955E2F3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AFE0F90CFC7BE623CE6BC48F588017BA /* permission_handler_apple.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = permission_handler_apple;
				PRODUCT_NAME = permission_handler_apple;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		819213CFC491D9A779B6089501887FAD /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 93C96FC37DD0F2078926E0C08A7D12E7 /* url_launcher_ios.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/url_launcher_ios";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = url_launcher_ios;
				INFOPLIST_FILE = "Target Support Files/url_launcher_ios/ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = url_launcher_ios_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		81E6D9FE80E955CE810EFACB3120CA97 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8233F69850A620ECE83BC1D740C5BFD8 /* qr_code_scanner.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/qr_code_scanner/qr_code_scanner-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/qr_code_scanner/qr_code_scanner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/qr_code_scanner/qr_code_scanner.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = qr_code_scanner;
				PRODUCT_NAME = qr_code_scanner;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		857BB3D5EB9F8B8635F38EE59B6FCA5D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DD8C8ACC853AF1B657D17AEC50E540A9 /* Pods-RunnerTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		86C5BBE7CBBA66350B4E55FDF50CE26D /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1FC96341216BBB5BBE3744FB1F35DEEE /* Pods-RunnerTests.profile.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		921E75BDAEEA3C04FB567462A1292EC6 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 65FFD225084FCE50ADEFD7A3E8A4D984 /* MTBBarcodeScanner.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/MTBBarcodeScanner/MTBBarcodeScanner-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/MTBBarcodeScanner/MTBBarcodeScanner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/MTBBarcodeScanner/MTBBarcodeScanner.modulemap";
				PRODUCT_MODULE_NAME = MTBBarcodeScanner;
				PRODUCT_NAME = MTBBarcodeScanner;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		B63FE56CB720293863AE7070FDD41F07 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4342F2FD6C28B7884D6544FF56C54D44 /* shared_preferences_foundation.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = shared_preferences_foundation;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = shared_preferences_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		BAD3A688C3B29EA7651D2E17CF1A9044 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7E2EF37CBC80FE81454E2B92519309C1 /* qr_code_scanner.debug.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/qr_code_scanner/qr_code_scanner-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/qr_code_scanner/qr_code_scanner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/qr_code_scanner/qr_code_scanner.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = qr_code_scanner;
				PRODUCT_NAME = qr_code_scanner;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		D09BFD3D35F6C946D9FAE547EC85B192 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AFE0F90CFC7BE623CE6BC48F588017BA /* permission_handler_apple.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = permission_handler_apple;
				PRODUCT_NAME = permission_handler_apple;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		E87564B8C174EABA04A6DB389005F125 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8233F69850A620ECE83BC1D740C5BFD8 /* qr_code_scanner.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/qr_code_scanner/qr_code_scanner-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/qr_code_scanner/qr_code_scanner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/qr_code_scanner/qr_code_scanner.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = qr_code_scanner;
				PRODUCT_NAME = qr_code_scanner;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		F371B7E6C0D6F91CE5D442FDD7440300 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C61AAF706076321407BE0DA7A1AAC994 /* shared_preferences_foundation.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = shared_preferences_foundation;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = shared_preferences_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		FB4B781D3E4B2E85C6448C7B77E40A53 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 070ED2FD37AF408D3C19A14BF90E80BF /* Flutter.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		FC5DE594D3F31672A41710105B4D238F /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F8547457089967DAC30C3130D4EDF7D1 /* Pods-Runner.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		38D3ADF0652CE382589F1F3CE1EEE84D /* Build configuration list for PBXNativeTarget "MTBBarcodeScanner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3DD953B17D38F68349BCF4C229F543B6 /* Debug */,
				28E7871BB8F4505A2762ACB51A210FF3 /* Profile */,
				921E75BDAEEA3C04FB567462A1292EC6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2B9E26EAE2CD392AD762421F663075A1 /* Debug */,
				5AF1AC88E5F655C93D1E3B5012F6FDC8 /* Profile */,
				63FAF33E1C55B71A5F5A8B3CC8749F99 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		51E44378D0EE45FC26653A27A9669C35 /* Build configuration list for PBXNativeTarget "shared_preferences_foundation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				46729596D3E861171179E26D7468B25C /* Debug */,
				768CE43E68DF2B5EF0C94D45F10B98DF /* Profile */,
				352238F70108F49434B51CFD9D646B3B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6B41E0EB7046A8350E615CE68E2B3477 /* Build configuration list for PBXNativeTarget "shared_preferences_foundation-shared_preferences_foundation_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F371B7E6C0D6F91CE5D442FDD7440300 /* Debug */,
				31CF6E7E1B226E53B3D3022B2496D0F8 /* Profile */,
				B63FE56CB720293863AE7070FDD41F07 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		89EFA95D7A42D94E2576D20D9240C679 /* Build configuration list for PBXNativeTarget "permission_handler_apple" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				11099184FBFDE376BB34AC1E682C4872 /* Debug */,
				D09BFD3D35F6C946D9FAE547EC85B192 /* Profile */,
				7BD1AB4DE0EECC079966841AD955E2F3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		94078A06C17E946ADC1F2C06726219E5 /* Build configuration list for PBXAggregateTarget "Flutter" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				70F1E3D053D812E3D7E8E74934CC9053 /* Debug */,
				76E053E47C7357CFF4EA96DF8337031F /* Profile */,
				FB4B781D3E4B2E85C6448C7B77E40A53 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A510A753FD3AF1785BCB45107877CB90 /* Build configuration list for PBXNativeTarget "url_launcher_ios" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3CD1AF7F3EB5E4F4EDE309AE4CFC8F28 /* Debug */,
				6D92E79F97BBCFD96CEE5C38A6C21349 /* Profile */,
				723189148229824216DB3B5536FF5DC9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B86BDF942AE09555F4C85B37CFEAE949 /* Build configuration list for PBXNativeTarget "qr_code_scanner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BAD3A688C3B29EA7651D2E17CF1A9044 /* Debug */,
				81E6D9FE80E955CE810EFACB3120CA97 /* Profile */,
				E87564B8C174EABA04A6DB389005F125 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		BE5025EBDC2FF7FE1A66F16C3C0D6445 /* Build configuration list for PBXNativeTarget "url_launcher_ios-url_launcher_ios_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				56B2356650607B22FE5E86C11DC3C5CE /* Debug */,
				418BCD7A28FB52D35E0BA822541241C0 /* Profile */,
				819213CFC491D9A779B6089501887FAD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D52099AA1537D0F3745166E1889F6CA3 /* Build configuration list for PBXNativeTarget "Pods-RunnerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				857BB3D5EB9F8B8635F38EE59B6FCA5D /* Debug */,
				86C5BBE7CBBA66350B4E55FDF50CE26D /* Profile */,
				2E4608C055BE4F4E675FECAEBC4CC2A4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DA775E1B0BF0A6C4F0F1AC62D60E4C41 /* Build configuration list for PBXNativeTarget "Pods-Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0F1546CA873CE89641848A549409E6E5 /* Debug */,
				42EEEF5DB1D4F9E361EC434FC1138672 /* Profile */,
				FC5DE594D3F31672A41710105B4D238F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E7E25C575961A90446FC59E7F36ACA9E /* Build configuration list for PBXNativeTarget "permission_handler_apple-permission_handler_apple_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				40D9065855C81C0AF0EA611178091202 /* Debug */,
				633338330E8910204C6A22D077F4E759 /* Profile */,
				78FF88BFAC1F034524695055FAA9173A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
