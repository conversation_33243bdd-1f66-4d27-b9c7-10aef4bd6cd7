// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXAggregateTarget section */
		1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 94078A06C17E946ADC1F2C06726219E5 /* Build configuration list for PBXAggregateTarget "Flutter" */;
			buildPhases = (
			);
			dependencies = (
			);
			name = Flutter;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		00ADA045A67231898185457CB946B52C /* SqfliteOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = BD9B412D151CC682B26827CA9B55FF4E /* SqfliteOperation.h */; settings = {ATTRIBUTES = (Project, ); }; };
		03432570AB88998A035398D46B01F04C /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */; };
		036DB75E39D0F256E8F12DB66249E151 /* MTBBarcodeScanner.m in Sources */ = {isa = PBXBuildFile; fileRef = 19942BB0CC239C074112446425810856 /* MTBBarcodeScanner.m */; };
		04AB4A5A9B70695B0A1752281FFF1337 /* Pods-Runner-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 4CB131F3BC1D6D2C79680B59CF30EF6B /* Pods-Runner-dummy.m */; };
		0683FB6A0EA2EF47F0B322524F95133A /* URLLaunchSession.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A2C761FD632B0D04AAA316BBBA68865 /* URLLaunchSession.swift */; };
		0AE22FF7663B5976F724157B0B55DD56 /* AppTrackingTransparencyPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = B0D84610B2E97CC8A42B1736D7C97995 /* AppTrackingTransparencyPermissionStrategy.m */; };
		0C67EF1B7B35D3378528B60173E29CA9 /* PermissionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 647F87845182CD87557ADF3C5D4CF70A /* PermissionManager.m */; };
		0E8149EAEA1794AC30BFB6B95E305546 /* PermissionHandlerPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = DF66CD837B30F09C306E991C17A9419C /* PermissionHandlerPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0EBF612BD9A9D28065172409F6498539 /* CriticalAlertsPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 1F8CB6D1ED8A587C954715A87ECE9F33 /* CriticalAlertsPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0F21C51CEF0C472E416D78035F9817A6 /* SpeechPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 5E77110E7A490450D30A3F7DA47F1143 /* SpeechPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0F296F88074DF3C09FB10E5EE971C0AC /* SqfliteDarwinResultSet.m in Sources */ = {isa = PBXBuildFile; fileRef = 1D2F4B3B6FB519776CEFB288CAF7B7D7 /* SqfliteDarwinResultSet.m */; };
		10ED346DDDB313562C91F8E5A7EA19A7 /* PhotoPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 82D4DA789C20B658578AE41E5F21D5B8 /* PhotoPermissionStrategy.m */; };
		121679B8561F25B70F958E1C449754C5 /* Codec.h in Headers */ = {isa = PBXBuildFile; fileRef = 43BF56477ADE3C13429AAE19E504A0CB /* Codec.h */; settings = {ATTRIBUTES = (Public, ); }; };
		12B38331E4F92C9745F0FF0F3FE8C1B6 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */; };
		1B2302FB19F28192AD3B5AD79CB5F8FA /* SqfliteDarwinDatabase.h in Headers */ = {isa = PBXBuildFile; fileRef = E74B613521B7A02D7D7C3937F8E54A2F /* SqfliteDarwinDatabase.h */; settings = {ATTRIBUTES = (Project, ); }; };
		26FDC6CCF00A736B47E27A0B325FC8AB /* PhotoPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = F11F1D546CF4126D1472FE55C26460BD /* PhotoPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2C8CCA1A7610BA60433377EE91A98CBC /* LocationPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = C84BC57F7083446D0FFF33969FFB781E /* LocationPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2E585A6D3EB862CB358F4236571AF949 /* AssistantPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = DF57375AD0E690502EFAC1749F9718D3 /* AssistantPermissionStrategy.m */; };
		309549EA2063A75373A0B3580CF8C424 /* MTBBarcodeScanner-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 4C3698AA06F487DB27EBF02A2BE1220B /* MTBBarcodeScanner-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		309946B1B86C448CC04E67DE4A63BEA8 /* SqfliteCursor.h in Headers */ = {isa = PBXBuildFile; fileRef = 5DBC10B5C3A492629483025F88DE6B5C /* SqfliteCursor.h */; settings = {ATTRIBUTES = (Project, ); }; };
		338767F71B0307EF3F31F230E1E35658 /* sqflite_darwin-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 9276D29448A6A17812000033FD7916EB /* sqflite_darwin-dummy.m */; };
		3470A0D6F3B46ABF0B991825AFF99FD4 /* SqfliteDarwinImport.h in Headers */ = {isa = PBXBuildFile; fileRef = 13DD4F106E83DF34E046C7207E62BEFA /* SqfliteDarwinImport.h */; settings = {ATTRIBUTES = (Project, ); }; };
		3875191124A81F2B1F777FA0F3B4B732 /* CriticalAlertsPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = DBA963B9F1550955F05E2156EB3ADAF8 /* CriticalAlertsPermissionStrategy.m */; };
		3A0887DA08578EF24E8373200679092A /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */; };
		4305445D8E6067B09985A36FAF7E1C36 /* UnknownPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 3EC7CD22F73E7722526C9335A5D9ACA9 /* UnknownPermissionStrategy.m */; };
		46AC153588A74102B5C92C37038834AF /* EventPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = A7047F9C9302FB94E533EFA57AF62734 /* EventPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		46EF214EC831A6FBBF7526791A0AF8E6 /* SqfliteDarwinDatabase.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D51EBD69FCF194299878D8C446024C9 /* SqfliteDarwinDatabase.m */; };
		480655E82282D63F49E426FDAE18D059 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = B1712D3CD1655DF7E52DB21318F9218B /* PrivacyInfo.xcprivacy */; };
		48ABC9CC212358D168731AEE56729598 /* shared_preferences_foundation-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 50155F7977E511D50B0B2BAF93687C2C /* shared_preferences_foundation-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4A117F054C60F408BECA9D19615C33A4 /* SqfliteDatabase.h in Headers */ = {isa = PBXBuildFile; fileRef = A3D893489072F557DA63B7D12FB8B857 /* SqfliteDatabase.h */; settings = {ATTRIBUTES = (Project, ); }; };
		4A43F54128D0FDF0B93C48446CE89B1D /* permission_handler_apple-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = E71ABCF88325940C94F45BC6BB19E1E9 /* permission_handler_apple-dummy.m */; };
		4D4CFC73824D102E82A8F3B0EBF43D39 /* ContactPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 0145B04FA94C3CE076DFCC4F9DA3F0D6 /* ContactPermissionStrategy.m */; };
		5872E3040AE2E5AC24619EB0568F6765 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = D30D42EE8831371F5DF21EADCE28D2FD /* PrivacyInfo.xcprivacy */; };
		588667FB9F813A253698E9593ACF4B3A /* Pods-Runner-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = BCC84BDE7260B712B097666E169A193C /* Pods-Runner-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		58FF267CBDAA4BDAA8279F31A9CB3C40 /* url_launcher_ios-url_launcher_ios_privacy in Resources */ = {isa = PBXBuildFile; fileRef = DBA6F34C072B134D3BE38983776DD1C3 /* url_launcher_ios-url_launcher_ios_privacy */; };
		5B06A55C0BD6EC7C93369250069299CA /* messages.g.swift in Sources */ = {isa = PBXBuildFile; fileRef = F24A71A10FB0C1433D505BDDEA3FE098 /* messages.g.swift */; };
		5C41E2EB060B6981260C5BBA07FE575C /* EventPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = B5DCD862CFD4226E82D29F1453C9252A /* EventPermissionStrategy.m */; };
		5E3B148B68E02791632CF8D79CF76772 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 6D597F39A13591ED952D71EB94365616 /* PrivacyInfo.xcprivacy */; };
		61ABBBBB217E71BFBC4E14F1520018BB /* SqfliteDarwinDatabaseQueue.h in Headers */ = {isa = PBXBuildFile; fileRef = EEED1492B2762197D9A742711FB5C9C9 /* SqfliteDarwinDatabaseQueue.h */; settings = {ATTRIBUTES = (Project, ); }; };
		6AC94655CFA316F8DC44B632AE169181 /* PhonePermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 35C032F2487C249C679D22E4C07D587E /* PhonePermissionStrategy.m */; };
		6C9A77B43CA0635F9E3B987759476CFE /* UnknownPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 703CB7A00152C2A1636DD8F83F88EFFD /* UnknownPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6F6257A610B1AB690E38F1CF7E9AF3F9 /* LocationPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 5B9D1D8DD74BF364BBAB2F8903FDA56E /* LocationPermissionStrategy.m */; };
		718A4F8C77639F1A12F101A9E2EE5E04 /* MTBBarcodeScanner.h in Headers */ = {isa = PBXBuildFile; fileRef = CE80AEE54213307F43286027D20CA42C /* MTBBarcodeScanner.h */; settings = {ATTRIBUTES = (Public, ); }; };
		74AE7AA5E923BC7D1F1CE6FB1AF221A2 /* PermissionManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 894C7D323921D329F340F359A9D07703 /* PermissionManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		75DA17B512459AF9817ED928870095AA /* MediaLibraryPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 6CC7085AC2CC82571D56CDA140DDDF6C /* MediaLibraryPermissionStrategy.m */; };
		76C7F3E8EB3F4ED704C3231BEE34E60B /* SqfliteDarwinDatabaseQueue.m in Sources */ = {isa = PBXBuildFile; fileRef = E2290EF425926DA555C1D3EE991F3F72 /* SqfliteDarwinDatabaseQueue.m */; };
		7778411698137CA267B6213857D67549 /* url_launcher_ios-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = A34B5B1391281A8A5AFECDE1F865513C /* url_launcher_ios-dummy.m */; };
		7C7D84E9240ADF7554BFFEC5DECA4D01 /* PermissionHandlerEnums.h in Headers */ = {isa = PBXBuildFile; fileRef = FA44411792D7645DE9D78C111E53BB9C /* PermissionHandlerEnums.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7F0345C3434305DFAEF184BBD6641FE1 /* QRViewFactory.swift in Sources */ = {isa = PBXBuildFile; fileRef = 144401D22740D5E1C8ACEA7DFF5FDE1E /* QRViewFactory.swift */; };
		7FDBF9325EC5DBAC0AC83D3890EFABFA /* SqflitePlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = E33BA4AD4E8EAE7C454D425580BBFB99 /* SqflitePlugin.h */; settings = {ATTRIBUTES = (Project, ); }; };
		8301C3E2174422AC905818E5DD71CA9F /* ContactPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 43E931C66EF3812EBDDD77F2BFA9D3A2 /* ContactPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		890944A46F735DF52163D3612DEA027A /* SqflitePlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = A0323AED000E7A119E2EF529B91EA936 /* SqflitePlugin.m */; };
		89B66AD0211DF422E28D4BA0FDFB5182 /* NotificationPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 4EF91C282B06A82C3A23A3D9307D7B49 /* NotificationPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8A377F9A1E700ACDFE60ECF38E7FBF60 /* SpeechPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 2930DEDA8CC35AD094AF73CD26DA709B /* SpeechPermissionStrategy.m */; };
		8BD319D3AAE00D70F1996967AE54CA82 /* SensorPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = FA6477CD109BA45F8DA19B070E2A1C59 /* SensorPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		91D8DDE968EC682F60B853529F6571F8 /* SqfliteImportPublic.h in Headers */ = {isa = PBXBuildFile; fileRef = 287382BC0928BB404A8741C004DA9767 /* SqfliteImportPublic.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9375CB6C22F320876DDD8E26A1B3AF4F /* SqfliteDarwinDB.h in Headers */ = {isa = PBXBuildFile; fileRef = B9C01C785C4B0E708399CD8CE4D5F590 /* SqfliteDarwinDB.h */; settings = {ATTRIBUTES = (Project, ); }; };
		97F91860804C4FF0F95BBCAEC6D85BAF /* url_launcher_ios-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 4A4B8BE73873871F76293E5ADC01184D /* url_launcher_ios-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9843123B99DF55D955E8AE2D4757F745 /* SqfliteImport.h in Headers */ = {isa = PBXBuildFile; fileRef = C16C08503D0854113CFBD556CCD615F7 /* SqfliteImport.h */; settings = {ATTRIBUTES = (Project, ); }; };
		9B8D0A18B5DD029F59DDC582B877B41F /* PhonePermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 35F7EAE9D164097E52AE6C2F4A954AC7 /* PhonePermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9B9CEE0D664C57FB31CCAA70D76A9E87 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */; };
		9F3376F43893107B68E2FB140B5A2268 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 355D0180080F026E55C27BAC37B41ABA /* AVFoundation.framework */; };
		9F896CD70A3D0BC0EC17E8DEA535FB56 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 3B6CF748676144838CA66B7F9DAB366F /* PrivacyInfo.xcprivacy */; };
		A0F8833ED8A6AC53A64E844315D69060 /* NotificationPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = BBDE455FC7455F28C12F6FE4C3544B6E /* NotificationPermissionStrategy.m */; };
		A2500528640D2211715118237B1E2B91 /* AudioVideoPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 3F7E2265A316479641572FD34B1B0510 /* AudioVideoPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A4B596C87351B1661A89E158A462B71F /* SqfliteDarwinDatabaseAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = A9F87DD9341CDF6B75488F14F6B662E5 /* SqfliteDarwinDatabaseAdditions.h */; settings = {ATTRIBUTES = (Project, ); }; };
		A7562EAC8F35C4207D6DF1FD21AE7023 /* URLLauncherPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = F0AFC892EA9B7DC46B7751F345A9A9C0 /* URLLauncherPlugin.swift */; };
		A8ECD0765398D636CF4557626B5B86FE /* SqfliteDarwinDatabaseAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 6F142EF2A576E1A5F8BFC73328C88366 /* SqfliteDarwinDatabaseAdditions.m */; };
		A985D47C7B6B78C64F0211B9E4AA916D /* AssistantPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 368049B256B735248803A4B2BB85F8B0 /* AssistantPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A9B4005CE5D708F1BBBD78996E2C897E /* QRView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 380142D33C83DE83638F9CA12B8CF9B3 /* QRView.swift */; };
		AA2937664B69935CCA0FDA5F26BD6165 /* qr_code_scanner-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = C86B8D88CEF66A6D0B1609EC06333B26 /* qr_code_scanner-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AB6B59C5DB5C485F5E1C0B3013806CF7 /* permission_handler_apple-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 9CE38E97C9BE9410C5E6EFE62B8FE49E /* permission_handler_apple-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AB97410CE8410A5B2279DDF4C43D7572 /* Launcher.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6D3E9BB9D5B20DFA49730DFC0B83FE70 /* Launcher.swift */; };
		B16449CBD26A3DE28038011FFDF3AF9C /* SqflitePluginPublic.h in Headers */ = {isa = PBXBuildFile; fileRef = 83D4A5D98FB7DB4B2C7692915D3712A7 /* SqflitePluginPublic.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B375EA128E6579366091BAA390BBDD34 /* Pods-RunnerTests-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 8758A56177F57A2EE30894AA6F81B31A /* Pods-RunnerTests-dummy.m */; };
		B7C67D475D63B3D6501863172F8AD35F /* shared_preferences_foundation-shared_preferences_foundation_privacy in Resources */ = {isa = PBXBuildFile; fileRef = 0652FCE3FC19056983AABE058B3CC45B /* shared_preferences_foundation-shared_preferences_foundation_privacy */; };
		BA582B8FC100119E53525F87B43AD8DC /* sqflite_darwin-sqflite_darwin_privacy in Resources */ = {isa = PBXBuildFile; fileRef = 071549DBDD4BFC0BD7D2715539015DC2 /* sqflite_darwin-sqflite_darwin_privacy */; };
		BC2C168999E585DBDF5CDD8E9E5217DD /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */; };
		BCE0C8639D6AE4A8792736C0ABB32A69 /* BackgroundRefreshStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 5E995D51D0556929EBF82577EF157FE2 /* BackgroundRefreshStrategy.m */; };
		BD200458BB8240FFC506B0F8EA215A47 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */; };
		BF0069CAFABF46710A32A9CC4B9D60FA /* MediaLibraryPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 979719CC2FECCDB71A67EC73685DE7A3 /* MediaLibraryPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C5316A62CBB753EFB2F8E37B58B73F4E /* FlutterQrPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = 6DADB1D4D61DAF099FA235FB789C00D8 /* FlutterQrPlugin.m */; };
		C69611CADFD4A4CBD493FA39DC1E7E66 /* SwiftFlutterQrPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = A10858B4532FB71B3DD2C33A4086DA96 /* SwiftFlutterQrPlugin.swift */; };
		CC766C2F06095124222B9EB87BEAEBBD /* FlutterQrPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = 07D1E4F6740B93ED8C3B4EC21F9189E2 /* FlutterQrPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		CC781030BA3802C6B47807E5ECCF5C69 /* MTBBarcodeScanner-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 906C5F1E88D3383DA6E6FFE986454BB4 /* MTBBarcodeScanner-dummy.m */; };
		CEBD84922D2CCEF26C272418EC3EB3A6 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */; };
		D2A52C6979E7137696C04242485D21EC /* shared_preferences_foundation-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 047AAE001C0D531007FE2D4B8E30389E /* shared_preferences_foundation-dummy.m */; };
		D48210CEBF1E59F5F8734D54807E1AC4 /* sqflite_darwin-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = CBA3F36E4F6654C02D6F7CC492852324 /* sqflite_darwin-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D73FD31018FE053DFECE1ADD9091607F /* AppTrackingTransparencyPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = E3B516F9A03A71EEF0295F84E0B19B50 /* AppTrackingTransparencyPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D89D87385F602BD315B4DD5B2EE4D70E /* SqfliteOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = D681BE485C6D45A1EBFCAD65D10E3BC4 /* SqfliteOperation.m */; };
		DBC3E1374D6F4F81FC8C6AB91DFD48DB /* StoragePermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A98D5F263AAECE490BDB00F748ECDC2 /* StoragePermissionStrategy.m */; };
		DD4809C7E7F2AFDE07CD71291F437F20 /* SqfliteCursor.m in Sources */ = {isa = PBXBuildFile; fileRef = 6CA243D93B80A9C7190419469EF5418B /* SqfliteCursor.m */; };
		DD54438BFB24943B91DBDD11A58D0A03 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F9E5AF19473BBB6D6B21A3B29833FDE0 /* QuartzCore.framework */; };
		DF972B5566EB8F594474408D1FEEBE03 /* qr_code_scanner-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A6ECCC4EF56BDADBF55239C29147D96 /* qr_code_scanner-dummy.m */; };
		E1290184B9BE7EF8E2EB64C3C38F39CF /* StoragePermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 2FF0D830AFA4A6D1347518B2ABAF630C /* StoragePermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E1F16FC5464194ABA094471ED06A3373 /* BluetoothPermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 37EB2543EC5121886734731425E14445 /* BluetoothPermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E517EE53DE3F1D6977E08A2040FB8C5D /* AudioVideoPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 817D9D58C8840742D5946EF8A0012705 /* AudioVideoPermissionStrategy.m */; };
		E6B934071CC8AAC4C2F13418E980DC83 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */; };
		E702B68F1D9F5FBBF77021A57B6642ED /* Codec.m in Sources */ = {isa = PBXBuildFile; fileRef = 57253328E9999FE84B36E25B31C0FB39 /* Codec.m */; };
		E9D09D2A0AE66B8FCF53A3B0B6171793 /* BluetoothPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 21B2D44B602F8433D11A11375B86ADA4 /* BluetoothPermissionStrategy.m */; };
		EAD247F99E22F9672E6556A00D0B5C03 /* SharedPreferencesPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = B510F264EA24C281E4C1ECB478BD55FA /* SharedPreferencesPlugin.swift */; };
		EB2DC96CDCF638AB89007D2DB0F3119A /* Pods-RunnerTests-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 2CB04EB554430E41E5A19EDE6D62C9F2 /* Pods-RunnerTests-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EBC219C8C0EB22FE57D8CAFD288BB75E /* BackgroundRefreshStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F78418CBE3C09B007676AE0F98525B /* BackgroundRefreshStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EBC24A935793317E4E9E956E3A9BF5EA /* messages.g.swift in Sources */ = {isa = PBXBuildFile; fileRef = AFDA41FF55539CD4C845993DAB6FF4F3 /* messages.g.swift */; };
		ED98596F5B2C920E3ABC28F50D71DB13 /* SqfliteDatabase.m in Sources */ = {isa = PBXBuildFile; fileRef = 7E8B60373BE57243ECBDDB28F047ECAA /* SqfliteDatabase.m */; };
		F0A225DB0B069780B9046F550CF964B7 /* PermissionStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = C167253EB84ED8DAE4F7E5FB537BFCFF /* PermissionStrategy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F26C1B07DD26AE16DC8C9DC496D9ACD6 /* SqfliteDarwinResultSet.h in Headers */ = {isa = PBXBuildFile; fileRef = 4CA640A93BB015DAD53AED8CF65DF458 /* SqfliteDarwinResultSet.h */; settings = {ATTRIBUTES = (Project, ); }; };
		F7DF9728D6CF306F2B6A9A90031BB0A7 /* SensorPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = D89130F0F4BD1D5599592749E3E11249 /* SensorPermissionStrategy.m */; };
		FA6596239E85709C1B9C53EB4D6B8A6F /* PermissionHandlerPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B73F606998EC70C8F9EB531E20DBAEE /* PermissionHandlerPlugin.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		0EDAE9521DD96EDF69198E3D7556FE07 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		169CFECA55A817B053D03B2D637EF305 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		16E2B217BF046B5E7E98767B01E1DD6C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		26D79FEB7A931BC52498AD9F01689605 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A45E503409C9530E1F5D12C293F4BD67;
			remoteInfo = permission_handler_apple;
		};
		3218DCC14D938F8AAE1A60D991B7C92D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9F5CAB8B25787EBAAFCC237AEDC74E2D;
			remoteInfo = "sqflite_darwin-sqflite_darwin_privacy";
		};
		5D285C64AE27E6B47E75D926E590F2E7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		6416F4756398A1CBF01E39175C8BB920 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = D83CD42EA57F97A225D24BDC7BD6B6F6;
			remoteInfo = MTBBarcodeScanner;
		};
		6AE00A2210A632358B754C37392B212A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8B74B458B450D74B75744B87BD747314;
			remoteInfo = "Pods-Runner";
		};
		6F2B7FCFC14967D07582D3ABF854311E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 517E8BED8B0E6D6FD078BD19B0A13893;
			remoteInfo = "permission_handler_apple-permission_handler_apple_privacy";
		};
		82F856DC476EA41AFFAF0A55E156C09E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E864A9DA72EED9027036A46ABD1822BC;
			remoteInfo = qr_code_scanner;
		};
		A5BA2FAF1B81ADBCC2FF35B0AEE8C554 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DF45E85925DF410BE416B32171F59C1F;
			remoteInfo = url_launcher_ios;
		};
		B0433267526D322E1FCEEB74F875011C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		C1D210459AF3FA427EB447D44A70CBBE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6178324C15B5DCE31C127429F0C8EE8F;
			remoteInfo = sqflite_darwin;
		};
		C435D2D5C500FC38FD689501505D5A19 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B6AF8B7CEAF6321719ABBC7E770624DA;
			remoteInfo = "shared_preferences_foundation-shared_preferences_foundation_privacy";
		};
		C7361595CBB6FCACB2AA2979BB22671C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = D83CD42EA57F97A225D24BDC7BD6B6F6;
			remoteInfo = MTBBarcodeScanner;
		};
		CA8E7D61B159F53F2DEB3B5C8FF68641 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AB5EE685B22D01885ADD930538E8DD3C;
			remoteInfo = shared_preferences_foundation;
		};
		D9F8EAAE6A33DF8D10A3C2A092BE218D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 43BE97C40504244259CF3C1D21E7EDB5;
			remoteInfo = "url_launcher_ios-url_launcher_ios_privacy";
		};
		DF636A2E8507C8825D1D8DB093F0F8D7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0145B04FA94C3CE076DFCC4F9DA3F0D6 /* ContactPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = ContactPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.m"; sourceTree = "<group>"; };
		026D5613CE61EBD09014496763B27D35 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/LICENSE"; sourceTree = "<group>"; };
		047AAE001C0D531007FE2D4B8E30389E /* shared_preferences_foundation-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "shared_preferences_foundation-dummy.m"; sourceTree = "<group>"; };
		053EF86AB46C0444BE3B3AECAF505728 /* sqflite_darwin.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = sqflite_darwin.podspec; path = "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		054B4435FD56A3C9F8FA92CD0E6F883D /* ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist"; sourceTree = "<group>"; };
		0652FCE3FC19056983AABE058B3CC45B /* shared_preferences_foundation-shared_preferences_foundation_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "shared_preferences_foundation-shared_preferences_foundation_privacy"; path = shared_preferences_foundation_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		071549DBDD4BFC0BD7D2715539015DC2 /* sqflite_darwin-sqflite_darwin_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "sqflite_darwin-sqflite_darwin_privacy"; path = sqflite_darwin_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		07D1E4F6740B93ED8C3B4EC21F9189E2 /* FlutterQrPlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FlutterQrPlugin.h; path = "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/FlutterQrPlugin.h"; sourceTree = "<group>"; };
		0D351F23C38290B0C3B49DC7310E8B45 /* Pods-Runner.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-Runner.modulemap"; sourceTree = "<group>"; };
		0EA5EEAF9BE9E00E7E7249E412BEDCAC /* qr_code_scanner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = qr_code_scanner.debug.xcconfig; sourceTree = "<group>"; };
		11C5C5A0BE02E3302A2DCA5259300CEF /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE"; sourceTree = "<group>"; };
		12F78418CBE3C09B007676AE0F98525B /* BackgroundRefreshStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = BackgroundRefreshStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.h"; sourceTree = "<group>"; };
		13DD4F106E83DF34E046C7207E62BEFA /* SqfliteDarwinImport.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteDarwinImport.h; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinImport.h"; sourceTree = "<group>"; };
		144401D22740D5E1C8ACEA7DFF5FDE1E /* QRViewFactory.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = QRViewFactory.swift; path = "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/QRViewFactory.swift"; sourceTree = "<group>"; };
		16754451CC068C973247E72B5EACF78C /* Pods-Runner-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-Runner-Info.plist"; sourceTree = "<group>"; };
		19942BB0CC239C074112446425810856 /* MTBBarcodeScanner.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MTBBarcodeScanner.m; path = Classes/ios/Scanners/MTBBarcodeScanner.m; sourceTree = "<group>"; };
		1ADB61306F50F60E65BED44878011822 /* Pods-RunnerTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-RunnerTests.release.xcconfig"; sourceTree = "<group>"; };
		1B3472A0D835D1EE8382CA6EA57BFED6 /* permission_handler_apple.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = permission_handler_apple.modulemap; sourceTree = "<group>"; };
		1D2F4B3B6FB519776CEFB288CAF7B7D7 /* SqfliteDarwinResultSet.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SqfliteDarwinResultSet.m; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.m"; sourceTree = "<group>"; };
		1F8CB6D1ED8A587C954715A87ECE9F33 /* CriticalAlertsPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = CriticalAlertsPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.h"; sourceTree = "<group>"; };
		1FC96341216BBB5BBE3744FB1F35DEEE /* Pods-RunnerTests.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-RunnerTests.profile.xcconfig"; sourceTree = "<group>"; };
		21B2D44B602F8433D11A11375B86ADA4 /* BluetoothPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = BluetoothPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.m"; sourceTree = "<group>"; };
		287382BC0928BB404A8741C004DA9767 /* SqfliteImportPublic.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteImportPublic.h; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h"; sourceTree = "<group>"; };
		2930DEDA8CC35AD094AF73CD26DA709B /* SpeechPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SpeechPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.m"; sourceTree = "<group>"; };
		2AD35495F0539645B05298768B095719 /* MTBBarcodeScanner */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = MTBBarcodeScanner; path = MTBBarcodeScanner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2B3AF9D90D4B088422687FFF4641CBC3 /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		2CB04EB554430E41E5A19EDE6D62C9F2 /* Pods-RunnerTests-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-RunnerTests-umbrella.h"; sourceTree = "<group>"; };
		2D1B2FEA76ABA898F1AE217811488F59 /* MTBBarcodeScanner-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "MTBBarcodeScanner-Info.plist"; sourceTree = "<group>"; };
		2DD8D7CABB2BCB913073DD00AE23E4D5 /* sqflite_darwin-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "sqflite_darwin-Info.plist"; sourceTree = "<group>"; };
		2F0908B8A5026151E2800777E4B17F20 /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		2FF0D830AFA4A6D1347518B2ABAF630C /* StoragePermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = StoragePermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.h"; sourceTree = "<group>"; };
		355D0180080F026E55C27BAC37B41ABA /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVFoundation.framework; sourceTree = DEVELOPER_DIR; };
		35C032F2487C249C679D22E4C07D587E /* PhonePermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = PhonePermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.m"; sourceTree = "<group>"; };
		35F7EAE9D164097E52AE6C2F4A954AC7 /* PhonePermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = PhonePermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.h"; sourceTree = "<group>"; };
		368049B256B735248803A4B2BB85F8B0 /* AssistantPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AssistantPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.h"; sourceTree = "<group>"; };
		37EB2543EC5121886734731425E14445 /* BluetoothPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = BluetoothPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.h"; sourceTree = "<group>"; };
		380142D33C83DE83638F9CA12B8CF9B3 /* QRView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = QRView.swift; path = "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/QRView.swift"; sourceTree = "<group>"; };
		3B13004126E0CAA508B8403CDED88837 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE"; sourceTree = "<group>"; };
		3B6CF748676144838CA66B7F9DAB366F /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		3D1513C97358C172579C03693E779437 /* shared_preferences_foundation.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = shared_preferences_foundation.podspec; path = "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		3E249AAAE8E92B5750DB3E8ADB534D8F /* Pods-RunnerTests-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-RunnerTests-acknowledgements.plist"; sourceTree = "<group>"; };
		3EC7CD22F73E7722526C9335A5D9ACA9 /* UnknownPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = UnknownPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.m"; sourceTree = "<group>"; };
		3F7E2265A316479641572FD34B1B0510 /* AudioVideoPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AudioVideoPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.h"; sourceTree = "<group>"; };
		424BE151FC838EC6E756EA5DC28458C9 /* Flutter.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; path = Flutter.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		43BF56477ADE3C13429AAE19E504A0CB /* Codec.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = Codec.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/util/Codec.h"; sourceTree = "<group>"; };
		43E931C66EF3812EBDDD77F2BFA9D3A2 /* ContactPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ContactPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.h"; sourceTree = "<group>"; };
		45E120F4A9F7D13802BA4D4E76E24EFC /* Pods-Runner-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-Runner-acknowledgements.plist"; sourceTree = "<group>"; };
		466D25C6DDF85681F71B3AFAC9E50867 /* ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist"; sourceTree = "<group>"; };
		4683C6A6BA60720F17CAD71C28988BDC /* permission_handler_apple-permission_handler_apple_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "permission_handler_apple-permission_handler_apple_privacy"; path = permission_handler_apple_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		49A077A1FB8FEFC484B8314B77DC2D56 /* sqflite_darwin.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = sqflite_darwin.debug.xcconfig; sourceTree = "<group>"; };
		4A4B8BE73873871F76293E5ADC01184D /* url_launcher_ios-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "url_launcher_ios-umbrella.h"; sourceTree = "<group>"; };
		4C3698AA06F487DB27EBF02A2BE1220B /* MTBBarcodeScanner-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "MTBBarcodeScanner-umbrella.h"; sourceTree = "<group>"; };
		4CA640A93BB015DAD53AED8CF65DF458 /* SqfliteDarwinResultSet.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteDarwinResultSet.h; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.h"; sourceTree = "<group>"; };
		4CB131F3BC1D6D2C79680B59CF30EF6B /* Pods-Runner-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-Runner-dummy.m"; sourceTree = "<group>"; };
		4CC19314F47EEC3204D1864FB60777CA /* shared_preferences_foundation.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = shared_preferences_foundation.release.xcconfig; sourceTree = "<group>"; };
		4EF91C282B06A82C3A23A3D9307D7B49 /* NotificationPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = NotificationPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.h"; sourceTree = "<group>"; };
		4FE20B1CF8B318A8D9591ED1587D7592 /* sqflite_darwin */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = sqflite_darwin; path = sqflite_darwin.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		50155F7977E511D50B0B2BAF93687C2C /* shared_preferences_foundation-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "shared_preferences_foundation-umbrella.h"; sourceTree = "<group>"; };
		5161BC9F31CAAFF6F9F96AE24883E03F /* permission_handler_apple.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = permission_handler_apple.debug.xcconfig; sourceTree = "<group>"; };
		57253328E9999FE84B36E25B31C0FB39 /* Codec.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = Codec.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/util/Codec.m"; sourceTree = "<group>"; };
		5B9D1D8DD74BF364BBAB2F8903FDA56E /* LocationPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = LocationPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.m"; sourceTree = "<group>"; };
		5DBC10B5C3A492629483025F88DE6B5C /* SqfliteCursor.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteCursor.h; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.h"; sourceTree = "<group>"; };
		5E77110E7A490450D30A3F7DA47F1143 /* SpeechPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SpeechPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.h"; sourceTree = "<group>"; };
		5E995D51D0556929EBF82577EF157FE2 /* BackgroundRefreshStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = BackgroundRefreshStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.m"; sourceTree = "<group>"; };
		611A55CFD7BCEFE72434A7C656553F84 /* url_launcher_ios.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = url_launcher_ios.debug.xcconfig; sourceTree = "<group>"; };
		647F87845182CD87557ADF3C5D4CF70A /* PermissionManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = PermissionManager.m; path = "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.m"; sourceTree = "<group>"; };
		65FFD225084FCE50ADEFD7A3E8A4D984 /* MTBBarcodeScanner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = MTBBarcodeScanner.release.xcconfig; sourceTree = "<group>"; };
		669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-Runner"; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		6A2C761FD632B0D04AAA316BBBA68865 /* URLLaunchSession.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = URLLaunchSession.swift; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.4/ios/url_launcher_ios/Sources/url_launcher_ios/URLLaunchSession.swift"; sourceTree = "<group>"; };
		6A6ECCC4EF56BDADBF55239C29147D96 /* qr_code_scanner-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "qr_code_scanner-dummy.m"; sourceTree = "<group>"; };
		6C3345B1B3CAEDF5B03B1F731FDC492E /* Pods-RunnerTests */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-RunnerTests"; path = Pods_RunnerTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		6CA243D93B80A9C7190419469EF5418B /* SqfliteCursor.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SqfliteCursor.m; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.m"; sourceTree = "<group>"; };
		6CC7085AC2CC82571D56CDA140DDDF6C /* MediaLibraryPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MediaLibraryPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.m"; sourceTree = "<group>"; };
		6D3E9BB9D5B20DFA49730DFC0B83FE70 /* Launcher.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Launcher.swift; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.4/ios/url_launcher_ios/Sources/url_launcher_ios/Launcher.swift"; sourceTree = "<group>"; };
		6D597F39A13591ED952D71EB94365616 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		6DADB1D4D61DAF099FA235FB789C00D8 /* FlutterQrPlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FlutterQrPlugin.m; path = "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/FlutterQrPlugin.m"; sourceTree = "<group>"; };
		6F142EF2A576E1A5F8BFC73328C88366 /* SqfliteDarwinDatabaseAdditions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SqfliteDarwinDatabaseAdditions.m; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.m"; sourceTree = "<group>"; };
		703CB7A00152C2A1636DD8F83F88EFFD /* UnknownPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = UnknownPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.h"; sourceTree = "<group>"; };
		70594C6B00220674F02DA361FE319B72 /* permission_handler_apple.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = permission_handler_apple.release.xcconfig; sourceTree = "<group>"; };
		7326493F968B9991FCBBDC681B8100AC /* ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist"; sourceTree = "<group>"; };
		74A07FCF8FCFE014A95E26DF5746DF63 /* README.md */ = {isa = PBXFileReference; includeInIndex = 1; name = README.md; path = "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/README.md"; sourceTree = "<group>"; };
		7B7C06D35B3BC2BD649AAA1A489E49DA /* url_launcher_ios */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = url_launcher_ios; path = url_launcher_ios.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		7C674D9032D1862A85BCE88DD77D7073 /* url_launcher_ios-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "url_launcher_ios-prefix.pch"; sourceTree = "<group>"; };
		7E8B60373BE57243ECBDDB28F047ECAA /* SqfliteDatabase.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SqfliteDatabase.m; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.m"; sourceTree = "<group>"; };
		7FA1EE3150AB7E2D7A76E646E7CA6C1E /* Pods-Runner-resources.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-Runner-resources.sh"; sourceTree = "<group>"; };
		7FE2DD00C4073CCF7565CEE96F2F8161 /* url_launcher_ios.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = url_launcher_ios.modulemap; sourceTree = "<group>"; };
		817671F15D4B947F617BD4EE8ACFC278 /* Pods-RunnerTests-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-RunnerTests-Info.plist"; sourceTree = "<group>"; };
		817D9D58C8840742D5946EF8A0012705 /* AudioVideoPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AudioVideoPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.m"; sourceTree = "<group>"; };
		82D4DA789C20B658578AE41E5F21D5B8 /* PhotoPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = PhotoPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.m"; sourceTree = "<group>"; };
		83D4A5D98FB7DB4B2C7692915D3712A7 /* SqflitePluginPublic.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqflitePluginPublic.h; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h"; sourceTree = "<group>"; };
		85F65888075ECD568E1B8A07145D7089 /* shared_preferences_foundation-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "shared_preferences_foundation-prefix.pch"; sourceTree = "<group>"; };
		8758A56177F57A2EE30894AA6F81B31A /* Pods-RunnerTests-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-RunnerTests-dummy.m"; sourceTree = "<group>"; };
		881F7B1FB6089CBB176262AFED629F66 /* sqflite_darwin.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = sqflite_darwin.modulemap; sourceTree = "<group>"; };
		894C7D323921D329F340F359A9D07703 /* PermissionManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = PermissionManager.h; path = "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.h"; sourceTree = "<group>"; };
		8A432929F7E93D3E1AA06A4A8668C699 /* Pods-RunnerTests-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-RunnerTests-acknowledgements.markdown"; sourceTree = "<group>"; };
		8D51EBD69FCF194299878D8C446024C9 /* SqfliteDarwinDatabase.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SqfliteDarwinDatabase.m; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.m"; sourceTree = "<group>"; };
		8D6B0B7D1C0E40984EC17A71B2FE5D63 /* sqflite_darwin.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = sqflite_darwin.release.xcconfig; sourceTree = "<group>"; };
		8EDC0AE1F9076AD2F7A1FDCD374A002C /* qr_code_scanner */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = qr_code_scanner; path = qr_code_scanner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		906C5F1E88D3383DA6E6FFE986454BB4 /* MTBBarcodeScanner-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "MTBBarcodeScanner-dummy.m"; sourceTree = "<group>"; };
		9276D29448A6A17812000033FD7916EB /* sqflite_darwin-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "sqflite_darwin-dummy.m"; sourceTree = "<group>"; };
		92900F972FE6ADAD91A529A224B81FBE /* Flutter.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Flutter.release.xcconfig; sourceTree = "<group>"; };
		93C6A45C6E4792269BE9BE0073839BF0 /* shared_preferences_foundation */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = shared_preferences_foundation; path = shared_preferences_foundation.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		979719CC2FECCDB71A67EC73685DE7A3 /* MediaLibraryPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MediaLibraryPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.h"; sourceTree = "<group>"; };
		9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		99A0C9B24340FA9598C1AE597015BA20 /* sqflite_darwin-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "sqflite_darwin-prefix.pch"; sourceTree = "<group>"; };
		9A98D5F263AAECE490BDB00F748ECDC2 /* StoragePermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = StoragePermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.m"; sourceTree = "<group>"; };
		9B73F606998EC70C8F9EB531E20DBAEE /* PermissionHandlerPlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = PermissionHandlerPlugin.m; path = "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.m"; sourceTree = "<group>"; };
		9C96F923D257ECF3D5FA15D0E8B013DA /* qr_code_scanner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = qr_code_scanner.release.xcconfig; sourceTree = "<group>"; };
		9CE38E97C9BE9410C5E6EFE62B8FE49E /* permission_handler_apple-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "permission_handler_apple-umbrella.h"; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		9F7370884AA95FF3F89A5EB57CC79CDC /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE"; sourceTree = "<group>"; };
		A0323AED000E7A119E2EF529B91EA936 /* SqflitePlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SqflitePlugin.m; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.m"; sourceTree = "<group>"; };
		A10858B4532FB71B3DD2C33A4086DA96 /* SwiftFlutterQrPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SwiftFlutterQrPlugin.swift; path = "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/SwiftFlutterQrPlugin.swift"; sourceTree = "<group>"; };
		A34B5B1391281A8A5AFECDE1F865513C /* url_launcher_ios-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "url_launcher_ios-dummy.m"; sourceTree = "<group>"; };
		A3D893489072F557DA63B7D12FB8B857 /* SqfliteDatabase.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteDatabase.h; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.h"; sourceTree = "<group>"; };
		A7047F9C9302FB94E533EFA57AF62734 /* EventPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = EventPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.h"; sourceTree = "<group>"; };
		A8FDBAA5DB30273D3A49EC23FF64001E /* Pods-Runner-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-Runner-acknowledgements.markdown"; sourceTree = "<group>"; };
		A9F87DD9341CDF6B75488F14F6B662E5 /* SqfliteDarwinDatabaseAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteDarwinDatabaseAdditions.h; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.h"; sourceTree = "<group>"; };
		AAA81655226C06A382C8295F02845641 /* Flutter.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Flutter.debug.xcconfig; sourceTree = "<group>"; };
		AFDA41FF55539CD4C845993DAB6FF4F3 /* messages.g.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = messages.g.swift; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift"; sourceTree = "<group>"; };
		B0D84610B2E97CC8A42B1736D7C97995 /* AppTrackingTransparencyPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AppTrackingTransparencyPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.m"; sourceTree = "<group>"; };
		B1712D3CD1655DF7E52DB21318F9218B /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		B2AB91A1647649E2E356BA92D2B413E3 /* url_launcher_ios.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = url_launcher_ios.release.xcconfig; sourceTree = "<group>"; };
		B510F264EA24C281E4C1ECB478BD55FA /* SharedPreferencesPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SharedPreferencesPlugin.swift; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift"; sourceTree = "<group>"; };
		B5DCD862CFD4226E82D29F1453C9252A /* EventPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = EventPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.m"; sourceTree = "<group>"; };
		B9C01C785C4B0E708399CD8CE4D5F590 /* SqfliteDarwinDB.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteDarwinDB.h; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDB.h"; sourceTree = "<group>"; };
		BBDE455FC7455F28C12F6FE4C3544B6E /* NotificationPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = NotificationPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.m"; sourceTree = "<group>"; };
		BCC84BDE7260B712B097666E169A193C /* Pods-Runner-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-Runner-umbrella.h"; sourceTree = "<group>"; };
		BD903B709DA4695BC467801EA60BDA6A /* shared_preferences_foundation.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = shared_preferences_foundation.modulemap; sourceTree = "<group>"; };
		BD9B412D151CC682B26827CA9B55FF4E /* SqfliteOperation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteOperation.h; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.h"; sourceTree = "<group>"; };
		C167253EB84ED8DAE4F7E5FB537BFCFF /* PermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = PermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PermissionStrategy.h"; sourceTree = "<group>"; };
		C16C08503D0854113CFBD556CCD615F7 /* SqfliteImport.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteImport.h; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteImport.h"; sourceTree = "<group>"; };
		C3E607D4244AD6D9ED532E69A6DA10DD /* MTBBarcodeScanner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = MTBBarcodeScanner.debug.xcconfig; sourceTree = "<group>"; };
		C7AD28D5FB25A8DEDF61F78996932FA6 /* permission_handler_apple */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = permission_handler_apple; path = permission_handler_apple.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		C84BC57F7083446D0FFF33969FFB781E /* LocationPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = LocationPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.h"; sourceTree = "<group>"; };
		C86B8D88CEF66A6D0B1609EC06333B26 /* qr_code_scanner-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "qr_code_scanner-umbrella.h"; sourceTree = "<group>"; };
		CBA3F36E4F6654C02D6F7CC492852324 /* sqflite_darwin-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "sqflite_darwin-umbrella.h"; sourceTree = "<group>"; };
		CC0EC500C228973BF0FFEBEABF00F313 /* qr_code_scanner-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "qr_code_scanner-prefix.pch"; sourceTree = "<group>"; };
		CE80AEE54213307F43286027D20CA42C /* MTBBarcodeScanner.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTBBarcodeScanner.h; path = Classes/ios/Scanners/MTBBarcodeScanner.h; sourceTree = "<group>"; };
		CF52CA143E92D09FA0FBAC5BE0CDE751 /* url_launcher_ios.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = url_launcher_ios.podspec; path = "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.4/ios/url_launcher_ios.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		CFA56F7544C7A03823E1D2D749934BFC /* Pods-Runner-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-Runner-frameworks.sh"; sourceTree = "<group>"; };
		D1670C95B945B1C630A9879C3BCE9E47 /* MTBBarcodeScanner-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "MTBBarcodeScanner-prefix.pch"; sourceTree = "<group>"; };
		D30D42EE8831371F5DF21EADCE28D2FD /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.4/ios/url_launcher_ios/Sources/url_launcher_ios/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		D681BE485C6D45A1EBFCAD65D10E3BC4 /* SqfliteOperation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SqfliteOperation.m; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.m"; sourceTree = "<group>"; };
		D89130F0F4BD1D5599592749E3E11249 /* SensorPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SensorPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.m"; sourceTree = "<group>"; };
		DB1ECFEBFBB7A83573ECFC861362CC79 /* permission_handler_apple-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "permission_handler_apple-Info.plist"; sourceTree = "<group>"; };
		DBA6F34C072B134D3BE38983776DD1C3 /* url_launcher_ios-url_launcher_ios_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "url_launcher_ios-url_launcher_ios_privacy"; path = url_launcher_ios_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		DBA963B9F1550955F05E2156EB3ADAF8 /* CriticalAlertsPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = CriticalAlertsPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.m"; sourceTree = "<group>"; };
		DD16258AA922411328E6CFE9951CD1EC /* ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist"; sourceTree = "<group>"; };
		DD356F9FF3341E75BB7EE9378F315F15 /* shared_preferences_foundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "shared_preferences_foundation-Info.plist"; sourceTree = "<group>"; };
		DD8C8ACC853AF1B657D17AEC50E540A9 /* Pods-RunnerTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-RunnerTests.debug.xcconfig"; sourceTree = "<group>"; };
		DF57375AD0E690502EFAC1749F9718D3 /* AssistantPermissionStrategy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AssistantPermissionStrategy.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.m"; sourceTree = "<group>"; };
		DF66CD837B30F09C306E991C17A9419C /* PermissionHandlerPlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = PermissionHandlerPlugin.h; path = "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.h"; sourceTree = "<group>"; };
		E0001C7180AAA7153BF2BA78B10B104B /* permission_handler_apple.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = permission_handler_apple.podspec; path = "../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/permission_handler_apple.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		E2290EF425926DA555C1D3EE991F3F72 /* SqfliteDarwinDatabaseQueue.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SqfliteDarwinDatabaseQueue.m; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.m"; sourceTree = "<group>"; };
		E22AFF2586022461EB6B52606B4F857E /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.4/LICENSE"; sourceTree = "<group>"; };
		E33BA4AD4E8EAE7C454D425580BBFB99 /* SqflitePlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqflitePlugin.h; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.h"; sourceTree = "<group>"; };
		E3B516F9A03A71EEF0295F84E0B19B50 /* AppTrackingTransparencyPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AppTrackingTransparencyPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.h"; sourceTree = "<group>"; };
		E71ABCF88325940C94F45BC6BB19E1E9 /* permission_handler_apple-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "permission_handler_apple-dummy.m"; sourceTree = "<group>"; };
		E74B613521B7A02D7D7C3937F8E54A2F /* SqfliteDarwinDatabase.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteDarwinDatabase.h; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.h"; sourceTree = "<group>"; };
		E9C5F640AD2476216F4F542B0AF5E13C /* Pods-RunnerTests.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-RunnerTests.modulemap"; sourceTree = "<group>"; };
		EC15C06D1E260EAF79CFE63BFFE43B81 /* permission_handler_apple-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "permission_handler_apple-prefix.pch"; sourceTree = "<group>"; };
		EEED1492B2762197D9A742711FB5C9C9 /* SqfliteDarwinDatabaseQueue.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteDarwinDatabaseQueue.h; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.h"; sourceTree = "<group>"; };
		F0AFC892EA9B7DC46B7751F345A9A9C0 /* URLLauncherPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = URLLauncherPlugin.swift; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.4/ios/url_launcher_ios/Sources/url_launcher_ios/URLLauncherPlugin.swift"; sourceTree = "<group>"; };
		F11F1D546CF4126D1472FE55C26460BD /* PhotoPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = PhotoPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.h"; sourceTree = "<group>"; };
		F24A71A10FB0C1433D505BDDEA3FE098 /* messages.g.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = messages.g.swift; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.4/ios/url_launcher_ios/Sources/url_launcher_ios/messages.g.swift"; sourceTree = "<group>"; };
		F29AA99C359386C0CD80E8BB48F5C297 /* qr_code_scanner.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = qr_code_scanner.podspec; path = "../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/qr_code_scanner.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		F8547457089967DAC30C3130D4EDF7D1 /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		F94B295CD57777CC6E09C8343656D6B1 /* MTBBarcodeScanner.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = MTBBarcodeScanner.modulemap; sourceTree = "<group>"; };
		F9E5AF19473BBB6D6B21A3B29833FDE0 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/QuartzCore.framework; sourceTree = DEVELOPER_DIR; };
		FA44411792D7645DE9D78C111E53BB9C /* PermissionHandlerEnums.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = PermissionHandlerEnums.h; path = "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerEnums.h"; sourceTree = "<group>"; };
		FA6477CD109BA45F8DA19B070E2A1C59 /* SensorPermissionStrategy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SensorPermissionStrategy.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.h"; sourceTree = "<group>"; };
		FB40CB4A8D08AF66F7E118DCF2904662 /* qr_code_scanner-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "qr_code_scanner-Info.plist"; sourceTree = "<group>"; };
		FB68C45DD987FFCA486B1F0147C06272 /* qr_code_scanner.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = qr_code_scanner.modulemap; sourceTree = "<group>"; };
		FC80D06C66C5AB600D6FF5A246D738E0 /* url_launcher_ios-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "url_launcher_ios-Info.plist"; sourceTree = "<group>"; };
		FCF84F8C05EE19EC2A6A0514C82DAB44 /* shared_preferences_foundation.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = shared_preferences_foundation.debug.xcconfig; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1276BE6C20E21A18F1141E6EEB538106 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		174C2354D7E2A8F78F06F5343CD02522 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BD200458BB8240FFC506B0F8EA215A47 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		199838C7FB159421A55417636F4ED3A5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2B7D7E5068BBE5986E6E46858588B9B1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				03432570AB88998A035398D46B01F04C /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2EF5CBD48B25E094D991FEACD9B3F97B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BC2C168999E585DBDF5CDD8E9E5217DD /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		413B37DCF7BA9F02D333644BD78D0235 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9F3376F43893107B68E2FB140B5A2268 /* AVFoundation.framework in Frameworks */,
				12B38331E4F92C9745F0FF0F3FE8C1B6 /* Foundation.framework in Frameworks */,
				DD54438BFB24943B91DBDD11A58D0A03 /* QuartzCore.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4380924F566AA01EB048DC15F9BC6D33 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEBD84922D2CCEF26C272418EC3EB3A6 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9747146C330459A9D0D5A5EFFD012568 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9B9CEE0D664C57FB31CCAA70D76A9E87 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9DDB915734BE1D460EF6061F991F7800 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C60368AE5AB595A6FB47AB86BB81F10B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E6B934071CC8AAC4C2F13418E980DC83 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F1B7BC9F5886A65417D42116EFFF55FA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3A0887DA08578EF24E8373200679092A /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F5B1631274A21EA2D1E8C6D278E8EA80 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		03ACF7202F8BAE6C156FB2920B09875C /* .. */ = {
			isa = PBXGroup;
			children = (
				D79CBD1F3680056EB9E31F7E1949E33D /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		03C5C200A0787E300053CFA8F53CA094 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				B649CEAE405D60AA4AD30C1AAE59C467 /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		066ADE5C2DFB1DB2AD8E6B55222B3BB6 /* darwin */ = {
			isa = PBXGroup;
			children = (
				9BE7A0756E88C1D99340AAD363C5885A /* sqflite_darwin */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		07717340D947F7D06F9664D3CE4D6455 /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				7430FEAE8A5207A213C661A043F69A90 /* Sources */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		0D5D73BD0D9771413970C90521AF67D4 /* Sources */ = {
			isa = PBXGroup;
			children = (
				CE608669D6AD5F7630CFEFD2E7966242 /* sqflite_darwin */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		0D9E57220B220D2461B58BDEF3066442 /* Resources */ = {
			isa = PBXGroup;
			children = (
				3B6CF748676144838CA66B7F9DAB366F /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		0E3712AF2B61CEC080AAE0E42B0F0E5E /* Sources */ = {
			isa = PBXGroup;
			children = (
				44E10EAA30ABD6997B152905D330BF1F /* sqflite_darwin */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		0EE5EC1ED2051D0A3A243570B7BB05F0 /* .. */ = {
			isa = PBXGroup;
			children = (
				436A6ED251BC2317244796DF2662A310 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		0F1DEBA1C29402D12C28B23103296314 /* .. */ = {
			isa = PBXGroup;
			children = (
				A55BDC62ECF91E59CAAC2D54E9A63185 /* .. */,
			);
			name = ..;
			path = "../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios";
			sourceTree = "<group>";
		};
		11C9F4631ACB58A59B85933F130A9824 /* url_launcher_ios */ = {
			isa = PBXGroup;
			children = (
				803579BE0898B982D83AE79B94C877F8 /* ios */,
			);
			name = url_launcher_ios;
			path = url_launcher_ios;
			sourceTree = "<group>";
		};
		1550278B233A7558092C3A8FCA2F8062 /* .. */ = {
			isa = PBXGroup;
			children = (
				C9F5420FD0CFB44D6E41A925F33A5F41 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		16EFAA218D9C89293DF22A4EF050B882 /* .. */ = {
			isa = PBXGroup;
			children = (
				6EECFB3A6DE0FF8C8BF7725BE9F0F155 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		1A645C26F7DF9DA48927A315F6CDDCF2 /* Resources */ = {
			isa = PBXGroup;
			children = (
				6D597F39A13591ED952D71EB94365616 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		1D02CD626A335AF9C6915010EC60A607 /* plugins */ = {
			isa = PBXGroup;
			children = (
				AF7810CE15148B98937310F3071B0331 /* shared_preferences_foundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		1DDD43033EFC91AE7E3E746FB898443A /* Pod */ = {
			isa = PBXGroup;
			children = (
				3B13004126E0CAA508B8403CDED88837 /* LICENSE */,
				74A07FCF8FCFE014A95E26DF5746DF63 /* README.md */,
				053EF86AB46C0444BE3B3AECAF505728 /* sqflite_darwin.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		1F437D0AA779A0AD29097EAADD015379 /* strategies */ = {
			isa = PBXGroup;
			children = (
				E3B516F9A03A71EEF0295F84E0B19B50 /* AppTrackingTransparencyPermissionStrategy.h */,
				B0D84610B2E97CC8A42B1736D7C97995 /* AppTrackingTransparencyPermissionStrategy.m */,
				368049B256B735248803A4B2BB85F8B0 /* AssistantPermissionStrategy.h */,
				DF57375AD0E690502EFAC1749F9718D3 /* AssistantPermissionStrategy.m */,
				3F7E2265A316479641572FD34B1B0510 /* AudioVideoPermissionStrategy.h */,
				817D9D58C8840742D5946EF8A0012705 /* AudioVideoPermissionStrategy.m */,
				12F78418CBE3C09B007676AE0F98525B /* BackgroundRefreshStrategy.h */,
				5E995D51D0556929EBF82577EF157FE2 /* BackgroundRefreshStrategy.m */,
				37EB2543EC5121886734731425E14445 /* BluetoothPermissionStrategy.h */,
				21B2D44B602F8433D11A11375B86ADA4 /* BluetoothPermissionStrategy.m */,
				43E931C66EF3812EBDDD77F2BFA9D3A2 /* ContactPermissionStrategy.h */,
				0145B04FA94C3CE076DFCC4F9DA3F0D6 /* ContactPermissionStrategy.m */,
				1F8CB6D1ED8A587C954715A87ECE9F33 /* CriticalAlertsPermissionStrategy.h */,
				DBA963B9F1550955F05E2156EB3ADAF8 /* CriticalAlertsPermissionStrategy.m */,
				A7047F9C9302FB94E533EFA57AF62734 /* EventPermissionStrategy.h */,
				B5DCD862CFD4226E82D29F1453C9252A /* EventPermissionStrategy.m */,
				C84BC57F7083446D0FFF33969FFB781E /* LocationPermissionStrategy.h */,
				5B9D1D8DD74BF364BBAB2F8903FDA56E /* LocationPermissionStrategy.m */,
				979719CC2FECCDB71A67EC73685DE7A3 /* MediaLibraryPermissionStrategy.h */,
				6CC7085AC2CC82571D56CDA140DDDF6C /* MediaLibraryPermissionStrategy.m */,
				4EF91C282B06A82C3A23A3D9307D7B49 /* NotificationPermissionStrategy.h */,
				BBDE455FC7455F28C12F6FE4C3544B6E /* NotificationPermissionStrategy.m */,
				C167253EB84ED8DAE4F7E5FB537BFCFF /* PermissionStrategy.h */,
				35F7EAE9D164097E52AE6C2F4A954AC7 /* PhonePermissionStrategy.h */,
				35C032F2487C249C679D22E4C07D587E /* PhonePermissionStrategy.m */,
				F11F1D546CF4126D1472FE55C26460BD /* PhotoPermissionStrategy.h */,
				82D4DA789C20B658578AE41E5F21D5B8 /* PhotoPermissionStrategy.m */,
				FA6477CD109BA45F8DA19B070E2A1C59 /* SensorPermissionStrategy.h */,
				D89130F0F4BD1D5599592749E3E11249 /* SensorPermissionStrategy.m */,
				5E77110E7A490450D30A3F7DA47F1143 /* SpeechPermissionStrategy.h */,
				2930DEDA8CC35AD094AF73CD26DA709B /* SpeechPermissionStrategy.m */,
				2FF0D830AFA4A6D1347518B2ABAF630C /* StoragePermissionStrategy.h */,
				9A98D5F263AAECE490BDB00F748ECDC2 /* StoragePermissionStrategy.m */,
				703CB7A00152C2A1636DD8F83F88EFFD /* UnknownPermissionStrategy.h */,
				3EC7CD22F73E7722526C9335A5D9ACA9 /* UnknownPermissionStrategy.m */,
			);
			name = strategies;
			path = strategies;
			sourceTree = "<group>";
		};
		20636934091401CBB21CB77E629AF4C6 /* Pods-RunnerTests */ = {
			isa = PBXGroup;
			children = (
				E9C5F640AD2476216F4F542B0AF5E13C /* Pods-RunnerTests.modulemap */,
				8A432929F7E93D3E1AA06A4A8668C699 /* Pods-RunnerTests-acknowledgements.markdown */,
				3E249AAAE8E92B5750DB3E8ADB534D8F /* Pods-RunnerTests-acknowledgements.plist */,
				8758A56177F57A2EE30894AA6F81B31A /* Pods-RunnerTests-dummy.m */,
				817671F15D4B947F617BD4EE8ACFC278 /* Pods-RunnerTests-Info.plist */,
				2CB04EB554430E41E5A19EDE6D62C9F2 /* Pods-RunnerTests-umbrella.h */,
				DD8C8ACC853AF1B657D17AEC50E540A9 /* Pods-RunnerTests.debug.xcconfig */,
				1FC96341216BBB5BBE3744FB1F35DEEE /* Pods-RunnerTests.profile.xcconfig */,
				1ADB61306F50F60E65BED44878011822 /* Pods-RunnerTests.release.xcconfig */,
			);
			name = "Pods-RunnerTests";
			path = "Target Support Files/Pods-RunnerTests";
			sourceTree = "<group>";
		};
		245FCA1815673D0C6D9611A6E2212AB7 /* .. */ = {
			isa = PBXGroup;
			children = (
				426B6217B5128AB12D28439B24155644 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		261495AD27C08A1958284089CA22D293 /* qrcode_safe */ = {
			isa = PBXGroup;
			children = (
				50D49BE83174B0A14E6FF1EA050F2F2E /* ios */,
			);
			name = qrcode_safe;
			path = qrcode_safe;
			sourceTree = "<group>";
		};
		26C24ABC60E9436AD5018D758576560E /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				3B9B8C3D9EDA2D5082937C19A0C175A7 /* darwin */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		276E2AB473127B865DBA65CFCB337EE6 /* ios */ = {
			isa = PBXGroup;
			children = (
				6E2A1692C24A98F164FE9F8F56B1EBCA /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		277C078AE28A9BF84EBD1AEDA2942623 /* sqflite_darwin */ = {
			isa = PBXGroup;
			children = (
				066ADE5C2DFB1DB2AD8E6B55222B3BB6 /* darwin */,
			);
			name = sqflite_darwin;
			path = sqflite_darwin;
			sourceTree = "<group>";
		};
		2829047AD1EA4CB57F8ABF850FDCD736 /* url_launcher_ios */ = {
			isa = PBXGroup;
			children = (
				4AB2768C32829BFDAA585573957F061A /* Resources */,
			);
			name = url_launcher_ios;
			path = url_launcher_ios;
			sourceTree = "<group>";
		};
		29219E408399FC28815F68B555DD4606 /* qr_code_scanner */ = {
			isa = PBXGroup;
			children = (
				6BC09A22467B65338B56F4865A33313A /* ios */,
			);
			name = qr_code_scanner;
			path = qr_code_scanner;
			sourceTree = "<group>";
		};
		2B1F9FD686CE86090FA87501838FAC8C /* Support Files */ = {
			isa = PBXGroup;
			children = (
				AAA81655226C06A382C8295F02845641 /* Flutter.debug.xcconfig */,
				92900F972FE6ADAD91A529A224B81FBE /* Flutter.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Pods/Target Support Files/Flutter";
			sourceTree = "<group>";
		};
		2B3EAF1E63219770693058FD6A2D6D97 /* .. */ = {
			isa = PBXGroup;
			children = (
				FF0015B1CE0126CA63A69144D150F8F1 /* .. */,
			);
			name = ..;
			path = "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources";
			sourceTree = "<group>";
		};
		2E3E1ECA5AE09EA840F817C1D641C902 /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				2B3EAF1E63219770693058FD6A2D6D97 /* .. */,
				EA3D65C580DC28726CD9B26C241CFE84 /* Pod */,
				B40EB9A39A95700AEAFFE2ADBB2774A0 /* Support Files */,
			);
			name = shared_preferences_foundation;
			path = ../.symlinks/plugins/shared_preferences_foundation/darwin;
			sourceTree = "<group>";
		};
		2F2B278E3780792559166E4B00514660 /* Pod */ = {
			isa = PBXGroup;
			children = (
				026D5613CE61EBD09014496763B27D35 /* LICENSE */,
				F29AA99C359386C0CD80E8BB48F5C297 /* qr_code_scanner.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		3236B80289497EA4523D7ABEE9850BE5 /* .. */ = {
			isa = PBXGroup;
			children = (
				6CC7FE0445179D136C84BB9B99636179 /* qrcode-safe */,
			);
			name = ..;
			path = .;
			sourceTree = "<group>";
		};
		357A6E287D22F3BFF03B548D044E07B1 /* plugins */ = {
			isa = PBXGroup;
			children = (
				26C24ABC60E9436AD5018D758576560E /* shared_preferences_foundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		3750F0C537D968DD5D262D8F49308BA7 /* qrcode-safe */ = {
			isa = PBXGroup;
			children = (
				3A93E7AD56FAF9F11F27A53E53E1AB21 /* qrcode_safe */,
			);
			name = "qrcode-safe";
			path = "qrcode-safe";
			sourceTree = "<group>";
		};
		37739D8FE31B277A16CDDEC966549856 /* sqflite_darwin */ = {
			isa = PBXGroup;
			children = (
				287382BC0928BB404A8741C004DA9767 /* SqfliteImportPublic.h */,
				83D4A5D98FB7DB4B2C7692915D3712A7 /* SqflitePluginPublic.h */,
			);
			name = sqflite_darwin;
			path = sqflite_darwin;
			sourceTree = "<group>";
		};
		37A9476458C91B5DCD270EF13C234E53 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				C4FBA307F225DC464C3AC099106F7DB0 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		3A93E7AD56FAF9F11F27A53E53E1AB21 /* qrcode_safe */ = {
			isa = PBXGroup;
			children = (
				F912E721FD6A26338A698525D8A0CF07 /* ios */,
			);
			name = qrcode_safe;
			path = qrcode_safe;
			sourceTree = "<group>";
		};
		3B9B8C3D9EDA2D5082937C19A0C175A7 /* darwin */ = {
			isa = PBXGroup;
			children = (
				07717340D947F7D06F9664D3CE4D6455 /* shared_preferences_foundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		3D0B2A1D684507853774C28409F62BEF /* sqflite_darwin */ = {
			isa = PBXGroup;
			children = (
				998D10CB1E6574AE01C6D717C09A5479 /* .. */,
				1DDD43033EFC91AE7E3E746FB898443A /* Pod */,
				FB0D984740B311E9B7D2BB0134F475EC /* Support Files */,
			);
			name = sqflite_darwin;
			path = ../.symlinks/plugins/sqflite_darwin/darwin;
			sourceTree = "<group>";
		};
		402CF82ECAE13B9052EEE7D3F3568753 /* Sources */ = {
			isa = PBXGroup;
			children = (
				D0CD7FDEECE04407514919A07E5FBC0F /* url_launcher_ios */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		4246DC4849A0CCF01339C8CCFA880FC5 /* Classes */ = {
			isa = PBXGroup;
			children = (
				07D1E4F6740B93ED8C3B4EC21F9189E2 /* FlutterQrPlugin.h */,
				6DADB1D4D61DAF099FA235FB789C00D8 /* FlutterQrPlugin.m */,
				380142D33C83DE83638F9CA12B8CF9B3 /* QRView.swift */,
				144401D22740D5E1C8ACEA7DFF5FDE1E /* QRViewFactory.swift */,
				A10858B4532FB71B3DD2C33A4086DA96 /* SwiftFlutterQrPlugin.swift */,
			);
			name = Classes;
			path = Classes;
			sourceTree = "<group>";
		};
		4258E8773ACAAA9B9B8EC039D64975A9 /* .. */ = {
			isa = PBXGroup;
			children = (
				A6CEF76F7FD7C455F60E2430445C2EB6 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		426B6217B5128AB12D28439B24155644 /* .. */ = {
			isa = PBXGroup;
			children = (
				9C938ABB603F49DAF9621C16D122CFCC /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		436A6ED251BC2317244796DF2662A310 /* .. */ = {
			isa = PBXGroup;
			children = (
				4A7762AA394825FB4F58CC349B4D5372 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		44E10EAA30ABD6997B152905D330BF1F /* sqflite_darwin */ = {
			isa = PBXGroup;
			children = (
				1A645C26F7DF9DA48927A315F6CDDCF2 /* Resources */,
			);
			name = sqflite_darwin;
			path = sqflite_darwin;
			sourceTree = "<group>";
		};
		4532AB1B2E92705E4642B53A8002BE15 /* .. */ = {
			isa = PBXGroup;
			children = (
				7924BE98CA5620A0070A1D0DD3DB894F /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		4824E202E496F1EB3367FD091399CAA8 /* .. */ = {
			isa = PBXGroup;
			children = (
				4D3522DE812480883169945948727457 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		48784BA740BD93553321C337ADC29C92 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				F733C06013DDD6E5B5E1694C37DDB5BD /* Pods-Runner */,
				20636934091401CBB21CB77E629AF4C6 /* Pods-RunnerTests */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		4A7762AA394825FB4F58CC349B4D5372 /* .. */ = {
			isa = PBXGroup;
			children = (
				BBFD9A51E007878EFEE923380359FAD7 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		4AB2768C32829BFDAA585573957F061A /* Resources */ = {
			isa = PBXGroup;
			children = (
				D30D42EE8831371F5DF21EADCE28D2FD /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		4B32943F07F8BFDAEE1597A77484FF54 /* ios */ = {
			isa = PBXGroup;
			children = (
				5B7478CC4F4C85774B74F2D605BFC17E /* Classes */,
				5FE3FF7091F652178875AED792022A16 /* Resources */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		4D3522DE812480883169945948727457 /* .. */ = {
			isa = PBXGroup;
			children = (
				982F7BC45D6316A05CC3340EA4FD5535 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		50D49BE83174B0A14E6FF1EA050F2F2E /* ios */ = {
			isa = PBXGroup;
			children = (
				6EC4CCD4A0E58731C89A5653125F5EEE /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		51FBC51C900127B5C2D1C960D0CA5549 /* url_launcher_ios */ = {
			isa = PBXGroup;
			children = (
				402CF82ECAE13B9052EEE7D3F3568753 /* Sources */,
			);
			name = url_launcher_ios;
			path = url_launcher_ios;
			sourceTree = "<group>";
		};
		54A0370EE2B23FFD2A44A284EC0E37EE /* ios */ = {
			isa = PBXGroup;
			children = (
				AF2F7ED12669BD627D3F02CF488DAD19 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		55798C1A68F08DF6CF518C2201907E64 /* sqflite_darwin */ = {
			isa = PBXGroup;
			children = (
				8CF293477CB7E9DA8F05FE38C96DD1D8 /* darwin */,
			);
			name = sqflite_darwin;
			path = sqflite_darwin;
			sourceTree = "<group>";
		};
		595D11BE53845B982098FDE02ED262F2 /* plugins */ = {
			isa = PBXGroup;
			children = (
				CA2829931BB37DE9811EE9DEAFEFCB1E /* permission_handler_apple */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		5B7478CC4F4C85774B74F2D605BFC17E /* Classes */ = {
			isa = PBXGroup;
			children = (
				FA44411792D7645DE9D78C111E53BB9C /* PermissionHandlerEnums.h */,
				DF66CD837B30F09C306E991C17A9419C /* PermissionHandlerPlugin.h */,
				9B73F606998EC70C8F9EB531E20DBAEE /* PermissionHandlerPlugin.m */,
				894C7D323921D329F340F359A9D07703 /* PermissionManager.h */,
				647F87845182CD87557ADF3C5D4CF70A /* PermissionManager.m */,
				1F437D0AA779A0AD29097EAADD015379 /* strategies */,
				9861B9F60079011804AB4C6D28E7C771 /* util */,
			);
			name = Classes;
			path = Classes;
			sourceTree = "<group>";
		};
		5C6254E8B2CAD79F7B5901C19BC46B97 /* ios */ = {
			isa = PBXGroup;
			children = (
				8882F6647595ED879BD28E3F554DE9E7 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		5D0220EB171A42A9284264C11D8AC933 /* .. */ = {
			isa = PBXGroup;
			children = (
				0EE5EC1ED2051D0A3A243570B7BB05F0 /* .. */,
			);
			name = ..;
			path = "../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios";
			sourceTree = "<group>";
		};
		5FE3FF7091F652178875AED792022A16 /* Resources */ = {
			isa = PBXGroup;
			children = (
				B1712D3CD1655DF7E52DB21318F9218B /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		6358405A576A64558BF25B4A3DE76175 /* plugins */ = {
			isa = PBXGroup;
			children = (
				73A058CFBB19A095216884BE5DD19923 /* url_launcher_ios */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		64EF6A7C91785049534F893D7A100C90 /* qrcode_safe */ = {
			isa = PBXGroup;
			children = (
				F0A408F14EB4543CB34CAFD4AEFC713F /* ios */,
			);
			name = qrcode_safe;
			path = qrcode_safe;
			sourceTree = "<group>";
		};
		651C8165A2C6985FB4D7BEB6FB1097DC /* qr_code_scanner */ = {
			isa = PBXGroup;
			children = (
				0F1DEBA1C29402D12C28B23103296314 /* .. */,
				2F2B278E3780792559166E4B00514660 /* Pod */,
				CA6762DAFBA3C9127F4591207D9CC40B /* Support Files */,
			);
			name = qr_code_scanner;
			path = ../.symlinks/plugins/qr_code_scanner/ios;
			sourceTree = "<group>";
		};
		66DCC80E7D570C06C5E6AFB4588AA7D1 /* qrcode_safe */ = {
			isa = PBXGroup;
			children = (
				5C6254E8B2CAD79F7B5901C19BC46B97 /* ios */,
			);
			name = qrcode_safe;
			path = qrcode_safe;
			sourceTree = "<group>";
		};
		6BC09A22467B65338B56F4865A33313A /* ios */ = {
			isa = PBXGroup;
			children = (
				4246DC4849A0CCF01339C8CCFA880FC5 /* Classes */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		6CC7FE0445179D136C84BB9B99636179 /* qrcode-safe */ = {
			isa = PBXGroup;
			children = (
				D993489DC73C75C5BBB7A97B0516207C /* qrcode_safe */,
			);
			name = "qrcode-safe";
			path = "qrcode-safe";
			sourceTree = "<group>";
		};
		6D0C0AC532889B975A1900B58E720967 /* Pod */ = {
			isa = PBXGroup;
			children = (
				11C5C5A0BE02E3302A2DCA5259300CEF /* LICENSE */,
				E0001C7180AAA7153BF2BA78B10B104B /* permission_handler_apple.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		6E2A1692C24A98F164FE9F8F56B1EBCA /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				6358405A576A64558BF25B4A3DE76175 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		6EC4CCD4A0E58731C89A5653125F5EEE /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				595D11BE53845B982098FDE02ED262F2 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		6EECFB3A6DE0FF8C8BF7725BE9F0F155 /* .. */ = {
			isa = PBXGroup;
			children = (
				97AF459772D6D130B52F024EA97366FE /* .. */,
				6FA53E9AB2AC59D7208BC8BCD6A048DF /* qrcode-safe */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		6FA53E9AB2AC59D7208BC8BCD6A048DF /* qrcode-safe */ = {
			isa = PBXGroup;
			children = (
				64EF6A7C91785049534F893D7A100C90 /* qrcode_safe */,
			);
			name = "qrcode-safe";
			path = "qrcode-safe";
			sourceTree = "<group>";
		};
		7045368040D4FB753950403EEEA71DE4 /* url_launcher_ios */ = {
			isa = PBXGroup;
			children = (
				B4E860FC8E12DB9D5EB6AD66E7E65A3F /* Sources */,
			);
			name = url_launcher_ios;
			path = url_launcher_ios;
			sourceTree = "<group>";
		};
		73A058CFBB19A095216884BE5DD19923 /* url_launcher_ios */ = {
			isa = PBXGroup;
			children = (
				BAB24C3C03F08D19EA5C01BE751DBE20 /* ios */,
			);
			name = url_launcher_ios;
			path = url_launcher_ios;
			sourceTree = "<group>";
		};
		7430FEAE8A5207A213C661A043F69A90 /* Sources */ = {
			isa = PBXGroup;
			children = (
				91EA04BD498B2B17B2A4DA612D0AA08D /* shared_preferences_foundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		7712C506AD144A6717856D0C74EDDBB0 /* Products */ = {
			isa = PBXGroup;
			children = (
				2AD35495F0539645B05298768B095719 /* MTBBarcodeScanner */,
				C7AD28D5FB25A8DEDF61F78996932FA6 /* permission_handler_apple */,
				4683C6A6BA60720F17CAD71C28988BDC /* permission_handler_apple-permission_handler_apple_privacy */,
				669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */,
				6C3345B1B3CAEDF5B03B1F731FDC492E /* Pods-RunnerTests */,
				8EDC0AE1F9076AD2F7A1FDCD374A002C /* qr_code_scanner */,
				93C6A45C6E4792269BE9BE0073839BF0 /* shared_preferences_foundation */,
				0652FCE3FC19056983AABE058B3CC45B /* shared_preferences_foundation-shared_preferences_foundation_privacy */,
				4FE20B1CF8B318A8D9591ED1587D7592 /* sqflite_darwin */,
				071549DBDD4BFC0BD7D2715539015DC2 /* sqflite_darwin-sqflite_darwin_privacy */,
				7B7C06D35B3BC2BD649AAA1A489E49DA /* url_launcher_ios */,
				DBA6F34C072B134D3BE38983776DD1C3 /* url_launcher_ios-url_launcher_ios_privacy */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		771C25A5FD93CFD333AD497624B8453A /* ios */ = {
			isa = PBXGroup;
			children = (
				8174019FC62787E9CE8B08DE9D422042 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		784247EE5E28154FBEFBF1F6C16C92A6 /* qrcode_safe */ = {
			isa = PBXGroup;
			children = (
				771C25A5FD93CFD333AD497624B8453A /* ios */,
			);
			name = qrcode_safe;
			path = qrcode_safe;
			sourceTree = "<group>";
		};
		7924BE98CA5620A0070A1D0DD3DB894F /* .. */ = {
			isa = PBXGroup;
			children = (
				8231A443F7C478568CF26F4E83679376 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		799A4FD7E46FC35D7DF3FCA9F89396D3 /* .. */ = {
			isa = PBXGroup;
			children = (
				4824E202E496F1EB3367FD091399CAA8 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		7D7E4AFDD1463799F679F21FE3A4EA61 /* .. */ = {
			isa = PBXGroup;
			children = (
				8A33A687D040AA82AD0130319457E66D /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		803579BE0898B982D83AE79B94C877F8 /* ios */ = {
			isa = PBXGroup;
			children = (
				51FBC51C900127B5C2D1C960D0CA5549 /* url_launcher_ios */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		8174019FC62787E9CE8B08DE9D422042 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				A98571E08C6F06F6AD05C16B9ED11F2C /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		8231A443F7C478568CF26F4E83679376 /* .. */ = {
			isa = PBXGroup;
			children = (
				3236B80289497EA4523D7ABEE9850BE5 /* .. */,
				3750F0C537D968DD5D262D8F49308BA7 /* qrcode-safe */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		857EA75B39A90A02779FC2A28F163AD5 /* .. */ = {
			isa = PBXGroup;
			children = (
				1550278B233A7558092C3A8FCA2F8062 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		873D6F114EDA14E5F388F4E693E2A63D /* darwin */ = {
			isa = PBXGroup;
			children = (
				CD1DD20887BCC6E2C11508FCD51D3AE2 /* shared_preferences_foundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		8882F6647595ED879BD28E3F554DE9E7 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				B5376647830C2601C5355D5E8800EE25 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		8A33A687D040AA82AD0130319457E66D /* .. */ = {
			isa = PBXGroup;
			children = (
				ECD21D242898FBCF13F4F15D4E37C75A /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		8CF293477CB7E9DA8F05FE38C96DD1D8 /* darwin */ = {
			isa = PBXGroup;
			children = (
				DB07C7336821E4B32A0C7C61DB82DCC9 /* sqflite_darwin */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		91EA04BD498B2B17B2A4DA612D0AA08D /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				0D9E57220B220D2461B58BDEF3066442 /* Resources */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		97AF459772D6D130B52F024EA97366FE /* .. */ = {
			isa = PBXGroup;
			children = (
				AD9B567AA73DAC1B693A1B3B79115287 /* qrcode-safe */,
			);
			name = ..;
			path = .;
			sourceTree = "<group>";
		};
		97E703CD3FD890FB3ACCB01C66B39688 /* Flutter */ = {
			isa = PBXGroup;
			children = (
				BB4572D4C6F28A119C66C4AEBBAEE98C /* Pod */,
				2B1F9FD686CE86090FA87501838FAC8C /* Support Files */,
			);
			name = Flutter;
			path = ../Flutter;
			sourceTree = "<group>";
		};
		982F7BC45D6316A05CC3340EA4FD5535 /* .. */ = {
			isa = PBXGroup;
			children = (
				9C52DF59F1018FAE8550BFC895DE2E21 /* qrcode-safe */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		9861B9F60079011804AB4C6D28E7C771 /* util */ = {
			isa = PBXGroup;
			children = (
				43BF56477ADE3C13429AAE19E504A0CB /* Codec.h */,
				57253328E9999FE84B36E25B31C0FB39 /* Codec.m */,
			);
			name = util;
			path = util;
			sourceTree = "<group>";
		};
		98960807D57F1B778C999EB012F93CD5 /* .. */ = {
			isa = PBXGroup;
			children = (
				857EA75B39A90A02779FC2A28F163AD5 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		998D10CB1E6574AE01C6D717C09A5479 /* .. */ = {
			isa = PBXGroup;
			children = (
				7D7E4AFDD1463799F679F21FE3A4EA61 /* .. */,
			);
			name = ..;
			path = "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources";
			sourceTree = "<group>";
		};
		9BE7A0756E88C1D99340AAD363C5885A /* sqflite_darwin */ = {
			isa = PBXGroup;
			children = (
				0E3712AF2B61CEC080AAE0E42B0F0E5E /* Sources */,
			);
			name = sqflite_darwin;
			path = sqflite_darwin;
			sourceTree = "<group>";
		};
		9C52DF59F1018FAE8550BFC895DE2E21 /* qrcode-safe */ = {
			isa = PBXGroup;
			children = (
				784247EE5E28154FBEFBF1F6C16C92A6 /* qrcode_safe */,
			);
			name = "qrcode-safe";
			path = "qrcode-safe";
			sourceTree = "<group>";
		};
		9C938ABB603F49DAF9621C16D122CFCC /* .. */ = {
			isa = PBXGroup;
			children = (
				4532AB1B2E92705E4642B53A8002BE15 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		9CA30CA653196F2C8A28B4801B2F4D4F /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				BDDB351BFC991B27DDA1CA910C4DDF39 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		A55BDC62ECF91E59CAAC2D54E9A63185 /* .. */ = {
			isa = PBXGroup;
			children = (
				799A4FD7E46FC35D7DF3FCA9F89396D3 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		A5AF152CFF6797697B1846320B6D8C3B /* Support Files */ = {
			isa = PBXGroup;
			children = (
				F94B295CD57777CC6E09C8343656D6B1 /* MTBBarcodeScanner.modulemap */,
				906C5F1E88D3383DA6E6FFE986454BB4 /* MTBBarcodeScanner-dummy.m */,
				2D1B2FEA76ABA898F1AE217811488F59 /* MTBBarcodeScanner-Info.plist */,
				D1670C95B945B1C630A9879C3BCE9E47 /* MTBBarcodeScanner-prefix.pch */,
				4C3698AA06F487DB27EBF02A2BE1220B /* MTBBarcodeScanner-umbrella.h */,
				C3E607D4244AD6D9ED532E69A6DA10DD /* MTBBarcodeScanner.debug.xcconfig */,
				65FFD225084FCE50ADEFD7A3E8A4D984 /* MTBBarcodeScanner.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/MTBBarcodeScanner";
			sourceTree = "<group>";
		};
		A6CEF76F7FD7C455F60E2430445C2EB6 /* .. */ = {
			isa = PBXGroup;
			children = (
				16EFAA218D9C89293DF22A4EF050B882 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		A98571E08C6F06F6AD05C16B9ED11F2C /* plugins */ = {
			isa = PBXGroup;
			children = (
				29219E408399FC28815F68B555DD4606 /* qr_code_scanner */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		AD9B567AA73DAC1B693A1B3B79115287 /* qrcode-safe */ = {
			isa = PBXGroup;
			children = (
				EE4206149C3FA379EEA778ED5ED9D275 /* qrcode_safe */,
			);
			name = "qrcode-safe";
			path = "qrcode-safe";
			sourceTree = "<group>";
		};
		AF2F7ED12669BD627D3F02CF488DAD19 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				357A6E287D22F3BFF03B548D044E07B1 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		AF7810CE15148B98937310F3071B0331 /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				873D6F114EDA14E5F388F4E693E2A63D /* darwin */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		B40EB9A39A95700AEAFFE2ADBB2774A0 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				466D25C6DDF85681F71B3AFAC9E50867 /* ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist */,
				BD903B709DA4695BC467801EA60BDA6A /* shared_preferences_foundation.modulemap */,
				047AAE001C0D531007FE2D4B8E30389E /* shared_preferences_foundation-dummy.m */,
				DD356F9FF3341E75BB7EE9378F315F15 /* shared_preferences_foundation-Info.plist */,
				85F65888075ECD568E1B8A07145D7089 /* shared_preferences_foundation-prefix.pch */,
				50155F7977E511D50B0B2BAF93687C2C /* shared_preferences_foundation-umbrella.h */,
				FCF84F8C05EE19EC2A6A0514C82DAB44 /* shared_preferences_foundation.debug.xcconfig */,
				4CC19314F47EEC3204D1864FB60777CA /* shared_preferences_foundation.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/shared_preferences_foundation";
			sourceTree = "<group>";
		};
		B4E860FC8E12DB9D5EB6AD66E7E65A3F /* Sources */ = {
			isa = PBXGroup;
			children = (
				2829047AD1EA4CB57F8ABF850FDCD736 /* url_launcher_ios */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		B5376647830C2601C5355D5E8800EE25 /* plugins */ = {
			isa = PBXGroup;
			children = (
				11C9F4631ACB58A59B85933F130A9824 /* url_launcher_ios */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		B649CEAE405D60AA4AD30C1AAE59C467 /* iOS */ = {
			isa = PBXGroup;
			children = (
				355D0180080F026E55C27BAC37B41ABA /* AVFoundation.framework */,
				9983E4145B10E571ED37DB4878CCD49C /* Foundation.framework */,
				F9E5AF19473BBB6D6B21A3B29833FDE0 /* QuartzCore.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		B8536683C9B0AE2E5EE0707EE13784CB /* Support Files */ = {
			isa = PBXGroup;
			children = (
				1B3472A0D835D1EE8382CA6EA57BFED6 /* permission_handler_apple.modulemap */,
				E71ABCF88325940C94F45BC6BB19E1E9 /* permission_handler_apple-dummy.m */,
				DB1ECFEBFBB7A83573ECFC861362CC79 /* permission_handler_apple-Info.plist */,
				EC15C06D1E260EAF79CFE63BFFE43B81 /* permission_handler_apple-prefix.pch */,
				9CE38E97C9BE9410C5E6EFE62B8FE49E /* permission_handler_apple-umbrella.h */,
				5161BC9F31CAAFF6F9F96AE24883E03F /* permission_handler_apple.debug.xcconfig */,
				70594C6B00220674F02DA361FE319B72 /* permission_handler_apple.release.xcconfig */,
				054B4435FD56A3C9F8FA92CD0E6F883D /* ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/permission_handler_apple";
			sourceTree = "<group>";
		};
		B9B70025B3C81899F14FD47EFD86BC95 /* .. */ = {
			isa = PBXGroup;
			children = (
				D80261E918EC9A49036AC453212FF164 /* qrcode-safe */,
			);
			name = ..;
			path = .;
			sourceTree = "<group>";
		};
		BAB24C3C03F08D19EA5C01BE751DBE20 /* ios */ = {
			isa = PBXGroup;
			children = (
				7045368040D4FB753950403EEEA71DE4 /* url_launcher_ios */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		BB4572D4C6F28A119C66C4AEBBAEE98C /* Pod */ = {
			isa = PBXGroup;
			children = (
				424BE151FC838EC6E756EA5DC28458C9 /* Flutter.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		BBFD9A51E007878EFEE923380359FAD7 /* .. */ = {
			isa = PBXGroup;
			children = (
				BC5AF63A1D5ABD55857410F585E1D8A2 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		BC5AF63A1D5ABD55857410F585E1D8A2 /* .. */ = {
			isa = PBXGroup;
			children = (
				E80762243985CEE2D2DC0051ACAAD3A2 /* qrcode-safe */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		BDDB351BFC991B27DDA1CA910C4DDF39 /* plugins */ = {
			isa = PBXGroup;
			children = (
				55798C1A68F08DF6CF518C2201907E64 /* sqflite_darwin */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		BE8DD75D11A8502A33ED6EA9A729750F /* qrcode_safe */ = {
			isa = PBXGroup;
			children = (
				276E2AB473127B865DBA65CFCB337EE6 /* ios */,
			);
			name = qrcode_safe;
			path = qrcode_safe;
			sourceTree = "<group>";
		};
		C2D7B8C85A7EE4330431CBC205B77F85 /* Sources */ = {
			isa = PBXGroup;
			children = (
				CC7F848A607014F0978F48CBA2AC5209 /* shared_preferences_foundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		C4FBA307F225DC464C3AC099106F7DB0 /* plugins */ = {
			isa = PBXGroup;
			children = (
				277C078AE28A9BF84EBD1AEDA2942623 /* sqflite_darwin */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		C7ECB82CEC1035BC30CB79A6F77CA6B0 /* Pod */ = {
			isa = PBXGroup;
			children = (
				E22AFF2586022461EB6B52606B4F857E /* LICENSE */,
				CF52CA143E92D09FA0FBAC5BE0CDE751 /* url_launcher_ios.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		C9F5420FD0CFB44D6E41A925F33A5F41 /* .. */ = {
			isa = PBXGroup;
			children = (
				B9B70025B3C81899F14FD47EFD86BC95 /* .. */,
				F4820D7FA91ACB58C48B303B92C40FA0 /* qrcode-safe */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		CA2829931BB37DE9811EE9DEAFEFCB1E /* permission_handler_apple */ = {
			isa = PBXGroup;
			children = (
				4B32943F07F8BFDAEE1597A77484FF54 /* ios */,
			);
			name = permission_handler_apple;
			path = permission_handler_apple;
			sourceTree = "<group>";
		};
		CA6762DAFBA3C9127F4591207D9CC40B /* Support Files */ = {
			isa = PBXGroup;
			children = (
				FB68C45DD987FFCA486B1F0147C06272 /* qr_code_scanner.modulemap */,
				6A6ECCC4EF56BDADBF55239C29147D96 /* qr_code_scanner-dummy.m */,
				FB40CB4A8D08AF66F7E118DCF2904662 /* qr_code_scanner-Info.plist */,
				CC0EC500C228973BF0FFEBEABF00F313 /* qr_code_scanner-prefix.pch */,
				C86B8D88CEF66A6D0B1609EC06333B26 /* qr_code_scanner-umbrella.h */,
				0EA5EEAF9BE9E00E7E7249E412BEDCAC /* qr_code_scanner.debug.xcconfig */,
				9C96F923D257ECF3D5FA15D0E8B013DA /* qr_code_scanner.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/qr_code_scanner";
			sourceTree = "<group>";
		};
		CC7F848A607014F0978F48CBA2AC5209 /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				AFDA41FF55539CD4C845993DAB6FF4F3 /* messages.g.swift */,
				B510F264EA24C281E4C1ECB478BD55FA /* SharedPreferencesPlugin.swift */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		CD1DD20887BCC6E2C11508FCD51D3AE2 /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				C2D7B8C85A7EE4330431CBC205B77F85 /* Sources */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		CE608669D6AD5F7630CFEFD2E7966242 /* sqflite_darwin */ = {
			isa = PBXGroup;
			children = (
				5DBC10B5C3A492629483025F88DE6B5C /* SqfliteCursor.h */,
				6CA243D93B80A9C7190419469EF5418B /* SqfliteCursor.m */,
				E74B613521B7A02D7D7C3937F8E54A2F /* SqfliteDarwinDatabase.h */,
				8D51EBD69FCF194299878D8C446024C9 /* SqfliteDarwinDatabase.m */,
				A9F87DD9341CDF6B75488F14F6B662E5 /* SqfliteDarwinDatabaseAdditions.h */,
				6F142EF2A576E1A5F8BFC73328C88366 /* SqfliteDarwinDatabaseAdditions.m */,
				EEED1492B2762197D9A742711FB5C9C9 /* SqfliteDarwinDatabaseQueue.h */,
				E2290EF425926DA555C1D3EE991F3F72 /* SqfliteDarwinDatabaseQueue.m */,
				B9C01C785C4B0E708399CD8CE4D5F590 /* SqfliteDarwinDB.h */,
				13DD4F106E83DF34E046C7207E62BEFA /* SqfliteDarwinImport.h */,
				4CA640A93BB015DAD53AED8CF65DF458 /* SqfliteDarwinResultSet.h */,
				1D2F4B3B6FB519776CEFB288CAF7B7D7 /* SqfliteDarwinResultSet.m */,
				A3D893489072F557DA63B7D12FB8B857 /* SqfliteDatabase.h */,
				7E8B60373BE57243ECBDDB28F047ECAA /* SqfliteDatabase.m */,
				C16C08503D0854113CFBD556CCD615F7 /* SqfliteImport.h */,
				BD9B412D151CC682B26827CA9B55FF4E /* SqfliteOperation.h */,
				D681BE485C6D45A1EBFCAD65D10E3BC4 /* SqfliteOperation.m */,
				E33BA4AD4E8EAE7C454D425580BBFB99 /* SqflitePlugin.h */,
				A0323AED000E7A119E2EF529B91EA936 /* SqflitePlugin.m */,
				D9AA9EE04FE5D3C7E937B28FAF6DB52F /* include */,
			);
			name = sqflite_darwin;
			path = sqflite_darwin;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				FECA513A1650CDCEDC85DDC229684B0C /* Development Pods */,
				03C5C200A0787E300053CFA8F53CA094 /* Frameworks */,
				E114EBD91E697317058312CA355665C8 /* Pods */,
				7712C506AD144A6717856D0C74EDDBB0 /* Products */,
				48784BA740BD93553321C337ADC29C92 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D0CD7FDEECE04407514919A07E5FBC0F /* url_launcher_ios */ = {
			isa = PBXGroup;
			children = (
				6D3E9BB9D5B20DFA49730DFC0B83FE70 /* Launcher.swift */,
				F24A71A10FB0C1433D505BDDEA3FE098 /* messages.g.swift */,
				F0AFC892EA9B7DC46B7751F345A9A9C0 /* URLLauncherPlugin.swift */,
				6A2C761FD632B0D04AAA316BBBA68865 /* URLLaunchSession.swift */,
			);
			name = url_launcher_ios;
			path = url_launcher_ios;
			sourceTree = "<group>";
		};
		D1C7FBB6F99B176ABB57534760E4C89B /* Support Files */ = {
			isa = PBXGroup;
			children = (
				7326493F968B9991FCBBDC681B8100AC /* ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist */,
				7FE2DD00C4073CCF7565CEE96F2F8161 /* url_launcher_ios.modulemap */,
				A34B5B1391281A8A5AFECDE1F865513C /* url_launcher_ios-dummy.m */,
				FC80D06C66C5AB600D6FF5A246D738E0 /* url_launcher_ios-Info.plist */,
				7C674D9032D1862A85BCE88DD77D7073 /* url_launcher_ios-prefix.pch */,
				4A4B8BE73873871F76293E5ADC01184D /* url_launcher_ios-umbrella.h */,
				611A55CFD7BCEFE72434A7C656553F84 /* url_launcher_ios.debug.xcconfig */,
				B2AB91A1647649E2E356BA92D2B413E3 /* url_launcher_ios.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/url_launcher_ios";
			sourceTree = "<group>";
		};
		D79CBD1F3680056EB9E31F7E1949E33D /* .. */ = {
			isa = PBXGroup;
			children = (
				EFD56EDB54D47A32A1672BA2A4645C8A /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		D80261E918EC9A49036AC453212FF164 /* qrcode-safe */ = {
			isa = PBXGroup;
			children = (
				BE8DD75D11A8502A33ED6EA9A729750F /* qrcode_safe */,
			);
			name = "qrcode-safe";
			path = "qrcode-safe";
			sourceTree = "<group>";
		};
		D993489DC73C75C5BBB7A97B0516207C /* qrcode_safe */ = {
			isa = PBXGroup;
			children = (
				54A0370EE2B23FFD2A44A284EC0E37EE /* ios */,
			);
			name = qrcode_safe;
			path = qrcode_safe;
			sourceTree = "<group>";
		};
		D9AA9EE04FE5D3C7E937B28FAF6DB52F /* include */ = {
			isa = PBXGroup;
			children = (
				37739D8FE31B277A16CDDEC966549856 /* sqflite_darwin */,
			);
			name = include;
			path = include;
			sourceTree = "<group>";
		};
		DB07C7336821E4B32A0C7C61DB82DCC9 /* sqflite_darwin */ = {
			isa = PBXGroup;
			children = (
				0D5D73BD0D9771413970C90521AF67D4 /* Sources */,
			);
			name = sqflite_darwin;
			path = sqflite_darwin;
			sourceTree = "<group>";
		};
		DD7175BD26EA4D66E0B8F534BBACC913 /* url_launcher_ios */ = {
			isa = PBXGroup;
			children = (
				F090A7A01D3AC85265F87A60E2F8DD75 /* .. */,
				C7ECB82CEC1035BC30CB79A6F77CA6B0 /* Pod */,
				D1C7FBB6F99B176ABB57534760E4C89B /* Support Files */,
			);
			name = url_launcher_ios;
			path = ../.symlinks/plugins/url_launcher_ios/ios;
			sourceTree = "<group>";
		};
		E06B95DCA0F5256C5F3DC656E0E96BB0 /* MTBBarcodeScanner */ = {
			isa = PBXGroup;
			children = (
				CE80AEE54213307F43286027D20CA42C /* MTBBarcodeScanner.h */,
				19942BB0CC239C074112446425810856 /* MTBBarcodeScanner.m */,
				A5AF152CFF6797697B1846320B6D8C3B /* Support Files */,
			);
			name = MTBBarcodeScanner;
			path = MTBBarcodeScanner;
			sourceTree = "<group>";
		};
		E114EBD91E697317058312CA355665C8 /* Pods */ = {
			isa = PBXGroup;
			children = (
				E06B95DCA0F5256C5F3DC656E0E96BB0 /* MTBBarcodeScanner */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		E80762243985CEE2D2DC0051ACAAD3A2 /* qrcode-safe */ = {
			isa = PBXGroup;
			children = (
				261495AD27C08A1958284089CA22D293 /* qrcode_safe */,
			);
			name = "qrcode-safe";
			path = "qrcode-safe";
			sourceTree = "<group>";
		};
		EA3D65C580DC28726CD9B26C241CFE84 /* Pod */ = {
			isa = PBXGroup;
			children = (
				9F7370884AA95FF3F89A5EB57CC79CDC /* LICENSE */,
				3D1513C97358C172579C03693E779437 /* shared_preferences_foundation.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		ECD21D242898FBCF13F4F15D4E37C75A /* .. */ = {
			isa = PBXGroup;
			children = (
				4258E8773ACAAA9B9B8EC039D64975A9 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		EE4206149C3FA379EEA778ED5ED9D275 /* qrcode_safe */ = {
			isa = PBXGroup;
			children = (
				F2EC5980D6AABA2C6BDFA5940D399932 /* ios */,
			);
			name = qrcode_safe;
			path = qrcode_safe;
			sourceTree = "<group>";
		};
		EFD56EDB54D47A32A1672BA2A4645C8A /* .. */ = {
			isa = PBXGroup;
			children = (
				98960807D57F1B778C999EB012F93CD5 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		F090A7A01D3AC85265F87A60E2F8DD75 /* .. */ = {
			isa = PBXGroup;
			children = (
				03ACF7202F8BAE6C156FB2920B09875C /* .. */,
			);
			name = ..;
			path = "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.4/ios/url_launcher_ios/Sources";
			sourceTree = "<group>";
		};
		F0A408F14EB4543CB34CAFD4AEFC713F /* ios */ = {
			isa = PBXGroup;
			children = (
				9CA30CA653196F2C8A28B4801B2F4D4F /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		F2EC5980D6AABA2C6BDFA5940D399932 /* ios */ = {
			isa = PBXGroup;
			children = (
				37A9476458C91B5DCD270EF13C234E53 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		F4820D7FA91ACB58C48B303B92C40FA0 /* qrcode-safe */ = {
			isa = PBXGroup;
			children = (
				66DCC80E7D570C06C5E6AFB4588AA7D1 /* qrcode_safe */,
			);
			name = "qrcode-safe";
			path = "qrcode-safe";
			sourceTree = "<group>";
		};
		F733C06013DDD6E5B5E1694C37DDB5BD /* Pods-Runner */ = {
			isa = PBXGroup;
			children = (
				0D351F23C38290B0C3B49DC7310E8B45 /* Pods-Runner.modulemap */,
				A8FDBAA5DB30273D3A49EC23FF64001E /* Pods-Runner-acknowledgements.markdown */,
				45E120F4A9F7D13802BA4D4E76E24EFC /* Pods-Runner-acknowledgements.plist */,
				4CB131F3BC1D6D2C79680B59CF30EF6B /* Pods-Runner-dummy.m */,
				CFA56F7544C7A03823E1D2D749934BFC /* Pods-Runner-frameworks.sh */,
				16754451CC068C973247E72B5EACF78C /* Pods-Runner-Info.plist */,
				7FA1EE3150AB7E2D7A76E646E7CA6C1E /* Pods-Runner-resources.sh */,
				BCC84BDE7260B712B097666E169A193C /* Pods-Runner-umbrella.h */,
				2F0908B8A5026151E2800777E4B17F20 /* Pods-Runner.debug.xcconfig */,
				2B3AF9D90D4B088422687FFF4641CBC3 /* Pods-Runner.profile.xcconfig */,
				F8547457089967DAC30C3130D4EDF7D1 /* Pods-Runner.release.xcconfig */,
			);
			name = "Pods-Runner";
			path = "Target Support Files/Pods-Runner";
			sourceTree = "<group>";
		};
		F8F8326D5796B0422EB86018A09D6B24 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				1D02CD626A335AF9C6915010EC60A607 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		F912E721FD6A26338A698525D8A0CF07 /* ios */ = {
			isa = PBXGroup;
			children = (
				F8F8326D5796B0422EB86018A09D6B24 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		FAE9557221C56047A9774BA892FE9198 /* permission_handler_apple */ = {
			isa = PBXGroup;
			children = (
				5D0220EB171A42A9284264C11D8AC933 /* .. */,
				6D0C0AC532889B975A1900B58E720967 /* Pod */,
				B8536683C9B0AE2E5EE0707EE13784CB /* Support Files */,
			);
			name = permission_handler_apple;
			path = ../.symlinks/plugins/permission_handler_apple/ios;
			sourceTree = "<group>";
		};
		FB0D984740B311E9B7D2BB0134F475EC /* Support Files */ = {
			isa = PBXGroup;
			children = (
				DD16258AA922411328E6CFE9951CD1EC /* ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist */,
				881F7B1FB6089CBB176262AFED629F66 /* sqflite_darwin.modulemap */,
				9276D29448A6A17812000033FD7916EB /* sqflite_darwin-dummy.m */,
				2DD8D7CABB2BCB913073DD00AE23E4D5 /* sqflite_darwin-Info.plist */,
				99A0C9B24340FA9598C1AE597015BA20 /* sqflite_darwin-prefix.pch */,
				CBA3F36E4F6654C02D6F7CC492852324 /* sqflite_darwin-umbrella.h */,
				49A077A1FB8FEFC484B8314B77DC2D56 /* sqflite_darwin.debug.xcconfig */,
				8D6B0B7D1C0E40984EC17A71B2FE5D63 /* sqflite_darwin.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/sqflite_darwin";
			sourceTree = "<group>";
		};
		FECA513A1650CDCEDC85DDC229684B0C /* Development Pods */ = {
			isa = PBXGroup;
			children = (
				97E703CD3FD890FB3ACCB01C66B39688 /* Flutter */,
				FAE9557221C56047A9774BA892FE9198 /* permission_handler_apple */,
				651C8165A2C6985FB4D7BEB6FB1097DC /* qr_code_scanner */,
				2E3E1ECA5AE09EA840F817C1D641C902 /* shared_preferences_foundation */,
				3D0B2A1D684507853774C28409F62BEF /* sqflite_darwin */,
				DD7175BD26EA4D66E0B8F534BBACC913 /* url_launcher_ios */,
			);
			name = "Development Pods";
			sourceTree = "<group>";
		};
		FF0015B1CE0126CA63A69144D150F8F1 /* .. */ = {
			isa = PBXGroup;
			children = (
				245FCA1815673D0C6D9611A6E2212AB7 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		04354693D111E28076CE5350505C5592 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				97F91860804C4FF0F95BBCAEC6D85BAF /* url_launcher_ios-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D658CCB2512BD58C633CB8B2095F6C3 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				48ABC9CC212358D168731AEE56729598 /* shared_preferences_foundation-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A2DC3141BD061D09686ED0B88893D020 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				718A4F8C77639F1A12F101A9E2EE5E04 /* MTBBarcodeScanner.h in Headers */,
				309549EA2063A75373A0B3580CF8C424 /* MTBBarcodeScanner-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A68C5F7E7ED2DC561D219562C09BB396 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D48210CEBF1E59F5F8734D54807E1AC4 /* sqflite_darwin-umbrella.h in Headers */,
				309946B1B86C448CC04E67DE4A63BEA8 /* SqfliteCursor.h in Headers */,
				1B2302FB19F28192AD3B5AD79CB5F8FA /* SqfliteDarwinDatabase.h in Headers */,
				A4B596C87351B1661A89E158A462B71F /* SqfliteDarwinDatabaseAdditions.h in Headers */,
				61ABBBBB217E71BFBC4E14F1520018BB /* SqfliteDarwinDatabaseQueue.h in Headers */,
				9375CB6C22F320876DDD8E26A1B3AF4F /* SqfliteDarwinDB.h in Headers */,
				3470A0D6F3B46ABF0B991825AFF99FD4 /* SqfliteDarwinImport.h in Headers */,
				F26C1B07DD26AE16DC8C9DC496D9ACD6 /* SqfliteDarwinResultSet.h in Headers */,
				4A117F054C60F408BECA9D19615C33A4 /* SqfliteDatabase.h in Headers */,
				9843123B99DF55D955E8AE2D4757F745 /* SqfliteImport.h in Headers */,
				91D8DDE968EC682F60B853529F6571F8 /* SqfliteImportPublic.h in Headers */,
				00ADA045A67231898185457CB946B52C /* SqfliteOperation.h in Headers */,
				7FDBF9325EC5DBAC0AC83D3890EFABFA /* SqflitePlugin.h in Headers */,
				B16449CBD26A3DE28038011FFDF3AF9C /* SqflitePluginPublic.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		ABB1D74E139AC5261B050DCE54387545 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D73FD31018FE053DFECE1ADD9091607F /* AppTrackingTransparencyPermissionStrategy.h in Headers */,
				A985D47C7B6B78C64F0211B9E4AA916D /* AssistantPermissionStrategy.h in Headers */,
				A2500528640D2211715118237B1E2B91 /* AudioVideoPermissionStrategy.h in Headers */,
				EBC219C8C0EB22FE57D8CAFD288BB75E /* BackgroundRefreshStrategy.h in Headers */,
				E1F16FC5464194ABA094471ED06A3373 /* BluetoothPermissionStrategy.h in Headers */,
				121679B8561F25B70F958E1C449754C5 /* Codec.h in Headers */,
				8301C3E2174422AC905818E5DD71CA9F /* ContactPermissionStrategy.h in Headers */,
				0EBF612BD9A9D28065172409F6498539 /* CriticalAlertsPermissionStrategy.h in Headers */,
				46AC153588A74102B5C92C37038834AF /* EventPermissionStrategy.h in Headers */,
				2C8CCA1A7610BA60433377EE91A98CBC /* LocationPermissionStrategy.h in Headers */,
				BF0069CAFABF46710A32A9CC4B9D60FA /* MediaLibraryPermissionStrategy.h in Headers */,
				89B66AD0211DF422E28D4BA0FDFB5182 /* NotificationPermissionStrategy.h in Headers */,
				AB6B59C5DB5C485F5E1C0B3013806CF7 /* permission_handler_apple-umbrella.h in Headers */,
				7C7D84E9240ADF7554BFFEC5DECA4D01 /* PermissionHandlerEnums.h in Headers */,
				0E8149EAEA1794AC30BFB6B95E305546 /* PermissionHandlerPlugin.h in Headers */,
				74AE7AA5E923BC7D1F1CE6FB1AF221A2 /* PermissionManager.h in Headers */,
				F0A225DB0B069780B9046F550CF964B7 /* PermissionStrategy.h in Headers */,
				9B8D0A18B5DD029F59DDC582B877B41F /* PhonePermissionStrategy.h in Headers */,
				26FDC6CCF00A736B47E27A0B325FC8AB /* PhotoPermissionStrategy.h in Headers */,
				8BD319D3AAE00D70F1996967AE54CA82 /* SensorPermissionStrategy.h in Headers */,
				0F21C51CEF0C472E416D78035F9817A6 /* SpeechPermissionStrategy.h in Headers */,
				E1290184B9BE7EF8E2EB64C3C38F39CF /* StoragePermissionStrategy.h in Headers */,
				6C9A77B43CA0635F9E3B987759476CFE /* UnknownPermissionStrategy.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		ACCDAE6E4376DF942535A81A1E498AA8 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				588667FB9F813A253698E9593ACF4B3A /* Pods-Runner-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EF19ECA05ADC6C5880AFF7ACCDE433F3 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CC766C2F06095124222B9EB87BEAEBBD /* FlutterQrPlugin.h in Headers */,
				AA2937664B69935CCA0FDA5F26BD6165 /* qr_code_scanner-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FC4DA9BD27A4914E75F803590E4C4B8B /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EB2DC96CDCF638AB89007D2DB0F3119A /* Pods-RunnerTests-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		3232F0C0E7C65B232832393F9ADDD8C3 /* Pods-RunnerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D52099AA1537D0F3745166E1889F6CA3 /* Build configuration list for PBXNativeTarget "Pods-RunnerTests" */;
			buildPhases = (
				FC4DA9BD27A4914E75F803590E4C4B8B /* Headers */,
				001CCB0FEA80CD1C6BF534C6D9C6283A /* Sources */,
				4380924F566AA01EB048DC15F9BC6D33 /* Frameworks */,
				4A765108DEAFDEBF078F71CDDBE3414E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				51315E034258410B06A8EF97E9D7E39C /* PBXTargetDependency */,
			);
			name = "Pods-RunnerTests";
			productName = Pods_RunnerTests;
			productReference = 6C3345B1B3CAEDF5B03B1F731FDC492E /* Pods-RunnerTests */;
			productType = "com.apple.product-type.framework";
		};
		43BE97C40504244259CF3C1D21E7EDB5 /* url_launcher_ios-url_launcher_ios_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 77A2AFCE4E6AE140D9C60A032C6D8B33 /* Build configuration list for PBXNativeTarget "url_launcher_ios-url_launcher_ios_privacy" */;
			buildPhases = (
				EFD67E73836EE5B72930963AAFA19C69 /* Sources */,
				199838C7FB159421A55417636F4ED3A5 /* Frameworks */,
				16D87942177204F3BE3753C3EC20FA64 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "url_launcher_ios-url_launcher_ios_privacy";
			productName = url_launcher_ios_privacy;
			productReference = DBA6F34C072B134D3BE38983776DD1C3 /* url_launcher_ios-url_launcher_ios_privacy */;
			productType = "com.apple.product-type.bundle";
		};
		517E8BED8B0E6D6FD078BD19B0A13893 /* permission_handler_apple-permission_handler_apple_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8FCD22AFD0157EBB2B49F3BC9CE14BC5 /* Build configuration list for PBXNativeTarget "permission_handler_apple-permission_handler_apple_privacy" */;
			buildPhases = (
				F34C4FAFD0510D49110E023B0DF66412 /* Sources */,
				F5B1631274A21EA2D1E8C6D278E8EA80 /* Frameworks */,
				2A301BE81A23537EAA30AB609E002AA9 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "permission_handler_apple-permission_handler_apple_privacy";
			productName = permission_handler_apple_privacy;
			productReference = 4683C6A6BA60720F17CAD71C28988BDC /* permission_handler_apple-permission_handler_apple_privacy */;
			productType = "com.apple.product-type.bundle";
		};
		6178324C15B5DCE31C127429F0C8EE8F /* sqflite_darwin */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 84AAE47AFE07B5E560E9FE58ABDEEEDA /* Build configuration list for PBXNativeTarget "sqflite_darwin" */;
			buildPhases = (
				A68C5F7E7ED2DC561D219562C09BB396 /* Headers */,
				85F611869208A646B549018BCB5A4528 /* Sources */,
				174C2354D7E2A8F78F06F5343CD02522 /* Frameworks */,
				2A31367DF3EAEB8BCF9F9579F4B65AF6 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				15233CDB4E52F71AD0096F3FA0C34A26 /* PBXTargetDependency */,
				4920522DACA5D54200DC139E94115413 /* PBXTargetDependency */,
			);
			name = sqflite_darwin;
			productName = sqflite_darwin;
			productReference = 4FE20B1CF8B318A8D9591ED1587D7592 /* sqflite_darwin */;
			productType = "com.apple.product-type.framework";
		};
		8B74B458B450D74B75744B87BD747314 /* Pods-Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F3B4C638A1D12D9740DBFA0984314635 /* Build configuration list for PBXNativeTarget "Pods-Runner" */;
			buildPhases = (
				ACCDAE6E4376DF942535A81A1E498AA8 /* Headers */,
				BC932C19FD2B008D3BE81C02BE269395 /* Sources */,
				F1B7BC9F5886A65417D42116EFFF55FA /* Frameworks */,
				C501A47D0A37DC5AC84D5AAA73F870C6 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1F1BB86174AB1E7C9BCEB7411D405800 /* PBXTargetDependency */,
				5605355258CD2752604ADA0709959634 /* PBXTargetDependency */,
				0C70A9F88AFFC1156F5F5D758F827907 /* PBXTargetDependency */,
				BE8E5C8EAA85434CA7DE0143B5B0B1FE /* PBXTargetDependency */,
				220C43BDC92BBF168C3C9E60F25B7368 /* PBXTargetDependency */,
				4D8924CF38FCCC1BEECC13CCE46DB87C /* PBXTargetDependency */,
				7F948BD84F0BAAA031EA69E66D87FB45 /* PBXTargetDependency */,
			);
			name = "Pods-Runner";
			productName = Pods_Runner;
			productReference = 669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */;
			productType = "com.apple.product-type.framework";
		};
		9F5CAB8B25787EBAAFCC237AEDC74E2D /* sqflite_darwin-sqflite_darwin_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CD157E8A1CBECC1336B2D185DFC72DEA /* Build configuration list for PBXNativeTarget "sqflite_darwin-sqflite_darwin_privacy" */;
			buildPhases = (
				895D725386C9E176443D8765ECB7CE34 /* Sources */,
				1276BE6C20E21A18F1141E6EEB538106 /* Frameworks */,
				C6FA04FB60C67DE2FFA4E1EE19B6310A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "sqflite_darwin-sqflite_darwin_privacy";
			productName = sqflite_darwin_privacy;
			productReference = 071549DBDD4BFC0BD7D2715539015DC2 /* sqflite_darwin-sqflite_darwin_privacy */;
			productType = "com.apple.product-type.bundle";
		};
		A45E503409C9530E1F5D12C293F4BD67 /* permission_handler_apple */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 89EFA95D7A42D94E2576D20D9240C679 /* Build configuration list for PBXNativeTarget "permission_handler_apple" */;
			buildPhases = (
				ABB1D74E139AC5261B050DCE54387545 /* Headers */,
				72B84C6503F1E5F4C6B4E8A6808DE491 /* Sources */,
				2EF5CBD48B25E094D991FEACD9B3F97B /* Frameworks */,
				C34E22EB1E5502C9809B1EFD361E4A7C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A0A1460734751F53474A1590228C97CD /* PBXTargetDependency */,
				B9ADC0FDAC089303DE096CAB6AF77A80 /* PBXTargetDependency */,
			);
			name = permission_handler_apple;
			productName = permission_handler_apple;
			productReference = C7AD28D5FB25A8DEDF61F78996932FA6 /* permission_handler_apple */;
			productType = "com.apple.product-type.framework";
		};
		AB5EE685B22D01885ADD930538E8DD3C /* shared_preferences_foundation */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 51E44378D0EE45FC26653A27A9669C35 /* Build configuration list for PBXNativeTarget "shared_preferences_foundation" */;
			buildPhases = (
				7D658CCB2512BD58C633CB8B2095F6C3 /* Headers */,
				413B07111961A3E5A9004A9C5FEDBC18 /* Sources */,
				C60368AE5AB595A6FB47AB86BB81F10B /* Frameworks */,
				B0C1F9DA0908C6404D0A7A4254A1F50F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				142CE392C758EF2683CEED50BAB1206D /* PBXTargetDependency */,
				7881C53FE7D05857C801D82166FE8A49 /* PBXTargetDependency */,
			);
			name = shared_preferences_foundation;
			productName = shared_preferences_foundation;
			productReference = 93C6A45C6E4792269BE9BE0073839BF0 /* shared_preferences_foundation */;
			productType = "com.apple.product-type.framework";
		};
		B6AF8B7CEAF6321719ABBC7E770624DA /* shared_preferences_foundation-shared_preferences_foundation_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2111E2728A33B30CCF9E68F74D020A6C /* Build configuration list for PBXNativeTarget "shared_preferences_foundation-shared_preferences_foundation_privacy" */;
			buildPhases = (
				0AAACB6E044054204A58164DBC536868 /* Sources */,
				9DDB915734BE1D460EF6061F991F7800 /* Frameworks */,
				00F2EEDDC1818FE09141A15545582D29 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "shared_preferences_foundation-shared_preferences_foundation_privacy";
			productName = shared_preferences_foundation_privacy;
			productReference = 0652FCE3FC19056983AABE058B3CC45B /* shared_preferences_foundation-shared_preferences_foundation_privacy */;
			productType = "com.apple.product-type.bundle";
		};
		D83CD42EA57F97A225D24BDC7BD6B6F6 /* MTBBarcodeScanner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 38D3ADF0652CE382589F1F3CE1EEE84D /* Build configuration list for PBXNativeTarget "MTBBarcodeScanner" */;
			buildPhases = (
				A2DC3141BD061D09686ED0B88893D020 /* Headers */,
				66DE37C37EBC0314D5F2100831DB5005 /* Sources */,
				413B37DCF7BA9F02D333644BD78D0235 /* Frameworks */,
				83F962F7A678B1CBC3883F8087B43359 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = MTBBarcodeScanner;
			productName = MTBBarcodeScanner;
			productReference = 2AD35495F0539645B05298768B095719 /* MTBBarcodeScanner */;
			productType = "com.apple.product-type.framework";
		};
		DF45E85925DF410BE416B32171F59C1F /* url_launcher_ios */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A510A753FD3AF1785BCB45107877CB90 /* Build configuration list for PBXNativeTarget "url_launcher_ios" */;
			buildPhases = (
				04354693D111E28076CE5350505C5592 /* Headers */,
				4A9CA9F71DA4F121DDB49A59FA1C644C /* Sources */,
				9747146C330459A9D0D5A5EFFD012568 /* Frameworks */,
				1D6B73C1303F55B339571CA9DD304EEE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F207B0D72E77210AF01B0156D32F3B92 /* PBXTargetDependency */,
				A700013D053E0882799EE9EA2D0C22E0 /* PBXTargetDependency */,
			);
			name = url_launcher_ios;
			productName = url_launcher_ios;
			productReference = 7B7C06D35B3BC2BD649AAA1A489E49DA /* url_launcher_ios */;
			productType = "com.apple.product-type.framework";
		};
		E864A9DA72EED9027036A46ABD1822BC /* qr_code_scanner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B86BDF942AE09555F4C85B37CFEAE949 /* Build configuration list for PBXNativeTarget "qr_code_scanner" */;
			buildPhases = (
				EF19ECA05ADC6C5880AFF7ACCDE433F3 /* Headers */,
				F6F77E0476D1D71C9FD36AC4550D74E8 /* Sources */,
				2B7D7E5068BBE5986E6E46858588B9B1 /* Frameworks */,
				D0AC7B5683373C4E1300FC0BC2B88787 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				34317285D3F25FECA493AD7B4E3ADB59 /* PBXTargetDependency */,
				C574CFAD276533AE3F33976FB625A180 /* PBXTargetDependency */,
			);
			name = qr_code_scanner;
			productName = qr_code_scanner;
			productReference = 8EDC0AE1F9076AD2F7A1FDCD374A002C /* qr_code_scanner */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = 7712C506AD144A6717856D0C74EDDBB0 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */,
				D83CD42EA57F97A225D24BDC7BD6B6F6 /* MTBBarcodeScanner */,
				A45E503409C9530E1F5D12C293F4BD67 /* permission_handler_apple */,
				517E8BED8B0E6D6FD078BD19B0A13893 /* permission_handler_apple-permission_handler_apple_privacy */,
				8B74B458B450D74B75744B87BD747314 /* Pods-Runner */,
				3232F0C0E7C65B232832393F9ADDD8C3 /* Pods-RunnerTests */,
				E864A9DA72EED9027036A46ABD1822BC /* qr_code_scanner */,
				AB5EE685B22D01885ADD930538E8DD3C /* shared_preferences_foundation */,
				B6AF8B7CEAF6321719ABBC7E770624DA /* shared_preferences_foundation-shared_preferences_foundation_privacy */,
				6178324C15B5DCE31C127429F0C8EE8F /* sqflite_darwin */,
				9F5CAB8B25787EBAAFCC237AEDC74E2D /* sqflite_darwin-sqflite_darwin_privacy */,
				DF45E85925DF410BE416B32171F59C1F /* url_launcher_ios */,
				43BE97C40504244259CF3C1D21E7EDB5 /* url_launcher_ios-url_launcher_ios_privacy */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00F2EEDDC1818FE09141A15545582D29 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9F896CD70A3D0BC0EC17E8DEA535FB56 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		16D87942177204F3BE3753C3EC20FA64 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5872E3040AE2E5AC24619EB0568F6765 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1D6B73C1303F55B339571CA9DD304EEE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				58FF267CBDAA4BDAA8279F31A9CB3C40 /* url_launcher_ios-url_launcher_ios_privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2A301BE81A23537EAA30AB609E002AA9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				480655E82282D63F49E426FDAE18D059 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2A31367DF3EAEB8BCF9F9579F4B65AF6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BA582B8FC100119E53525F87B43AD8DC /* sqflite_darwin-sqflite_darwin_privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4A765108DEAFDEBF078F71CDDBE3414E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		83F962F7A678B1CBC3883F8087B43359 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B0C1F9DA0908C6404D0A7A4254A1F50F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B7C67D475D63B3D6501863172F8AD35F /* shared_preferences_foundation-shared_preferences_foundation_privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C34E22EB1E5502C9809B1EFD361E4A7C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C501A47D0A37DC5AC84D5AAA73F870C6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C6FA04FB60C67DE2FFA4E1EE19B6310A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5E3B148B68E02791632CF8D79CF76772 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D0AC7B5683373C4E1300FC0BC2B88787 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		001CCB0FEA80CD1C6BF534C6D9C6283A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B375EA128E6579366091BAA390BBDD34 /* Pods-RunnerTests-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0AAACB6E044054204A58164DBC536868 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		413B07111961A3E5A9004A9C5FEDBC18 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EBC24A935793317E4E9E956E3A9BF5EA /* messages.g.swift in Sources */,
				D2A52C6979E7137696C04242485D21EC /* shared_preferences_foundation-dummy.m in Sources */,
				EAD247F99E22F9672E6556A00D0B5C03 /* SharedPreferencesPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4A9CA9F71DA4F121DDB49A59FA1C644C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AB97410CE8410A5B2279DDF4C43D7572 /* Launcher.swift in Sources */,
				5B06A55C0BD6EC7C93369250069299CA /* messages.g.swift in Sources */,
				7778411698137CA267B6213857D67549 /* url_launcher_ios-dummy.m in Sources */,
				A7562EAC8F35C4207D6DF1FD21AE7023 /* URLLauncherPlugin.swift in Sources */,
				0683FB6A0EA2EF47F0B322524F95133A /* URLLaunchSession.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		66DE37C37EBC0314D5F2100831DB5005 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				036DB75E39D0F256E8F12DB66249E151 /* MTBBarcodeScanner.m in Sources */,
				CC781030BA3802C6B47807E5ECCF5C69 /* MTBBarcodeScanner-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		72B84C6503F1E5F4C6B4E8A6808DE491 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0AE22FF7663B5976F724157B0B55DD56 /* AppTrackingTransparencyPermissionStrategy.m in Sources */,
				2E585A6D3EB862CB358F4236571AF949 /* AssistantPermissionStrategy.m in Sources */,
				E517EE53DE3F1D6977E08A2040FB8C5D /* AudioVideoPermissionStrategy.m in Sources */,
				BCE0C8639D6AE4A8792736C0ABB32A69 /* BackgroundRefreshStrategy.m in Sources */,
				E9D09D2A0AE66B8FCF53A3B0B6171793 /* BluetoothPermissionStrategy.m in Sources */,
				E702B68F1D9F5FBBF77021A57B6642ED /* Codec.m in Sources */,
				4D4CFC73824D102E82A8F3B0EBF43D39 /* ContactPermissionStrategy.m in Sources */,
				3875191124A81F2B1F777FA0F3B4B732 /* CriticalAlertsPermissionStrategy.m in Sources */,
				5C41E2EB060B6981260C5BBA07FE575C /* EventPermissionStrategy.m in Sources */,
				6F6257A610B1AB690E38F1CF7E9AF3F9 /* LocationPermissionStrategy.m in Sources */,
				75DA17B512459AF9817ED928870095AA /* MediaLibraryPermissionStrategy.m in Sources */,
				A0F8833ED8A6AC53A64E844315D69060 /* NotificationPermissionStrategy.m in Sources */,
				4A43F54128D0FDF0B93C48446CE89B1D /* permission_handler_apple-dummy.m in Sources */,
				FA6596239E85709C1B9C53EB4D6B8A6F /* PermissionHandlerPlugin.m in Sources */,
				0C67EF1B7B35D3378528B60173E29CA9 /* PermissionManager.m in Sources */,
				6AC94655CFA316F8DC44B632AE169181 /* PhonePermissionStrategy.m in Sources */,
				10ED346DDDB313562C91F8E5A7EA19A7 /* PhotoPermissionStrategy.m in Sources */,
				F7DF9728D6CF306F2B6A9A90031BB0A7 /* SensorPermissionStrategy.m in Sources */,
				8A377F9A1E700ACDFE60ECF38E7FBF60 /* SpeechPermissionStrategy.m in Sources */,
				DBC3E1374D6F4F81FC8C6AB91DFD48DB /* StoragePermissionStrategy.m in Sources */,
				4305445D8E6067B09985A36FAF7E1C36 /* UnknownPermissionStrategy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		85F611869208A646B549018BCB5A4528 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				338767F71B0307EF3F31F230E1E35658 /* sqflite_darwin-dummy.m in Sources */,
				DD4809C7E7F2AFDE07CD71291F437F20 /* SqfliteCursor.m in Sources */,
				46EF214EC831A6FBBF7526791A0AF8E6 /* SqfliteDarwinDatabase.m in Sources */,
				A8ECD0765398D636CF4557626B5B86FE /* SqfliteDarwinDatabaseAdditions.m in Sources */,
				76C7F3E8EB3F4ED704C3231BEE34E60B /* SqfliteDarwinDatabaseQueue.m in Sources */,
				0F296F88074DF3C09FB10E5EE971C0AC /* SqfliteDarwinResultSet.m in Sources */,
				ED98596F5B2C920E3ABC28F50D71DB13 /* SqfliteDatabase.m in Sources */,
				D89D87385F602BD315B4DD5B2EE4D70E /* SqfliteOperation.m in Sources */,
				890944A46F735DF52163D3612DEA027A /* SqflitePlugin.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		895D725386C9E176443D8765ECB7CE34 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BC932C19FD2B008D3BE81C02BE269395 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				04AB4A5A9B70695B0A1752281FFF1337 /* Pods-Runner-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EFD67E73836EE5B72930963AAFA19C69 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F34C4FAFD0510D49110E023B0DF66412 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F6F77E0476D1D71C9FD36AC4550D74E8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C5316A62CBB753EFB2F8E37B58B73F4E /* FlutterQrPlugin.m in Sources */,
				DF972B5566EB8F594474408D1FEEBE03 /* qr_code_scanner-dummy.m in Sources */,
				A9B4005CE5D708F1BBBD78996E2C897E /* QRView.swift in Sources */,
				7F0345C3434305DFAEF184BBD6641FE1 /* QRViewFactory.swift in Sources */,
				C69611CADFD4A4CBD493FA39DC1E7E66 /* SwiftFlutterQrPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		0C70A9F88AFFC1156F5F5D758F827907 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = permission_handler_apple;
			target = A45E503409C9530E1F5D12C293F4BD67 /* permission_handler_apple */;
			targetProxy = 26D79FEB7A931BC52498AD9F01689605 /* PBXContainerItemProxy */;
		};
		142CE392C758EF2683CEED50BAB1206D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = B0433267526D322E1FCEEB74F875011C /* PBXContainerItemProxy */;
		};
		15233CDB4E52F71AD0096F3FA0C34A26 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 16E2B217BF046B5E7E98767B01E1DD6C /* PBXContainerItemProxy */;
		};
		1F1BB86174AB1E7C9BCEB7411D405800 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 169CFECA55A817B053D03B2D637EF305 /* PBXContainerItemProxy */;
		};
		220C43BDC92BBF168C3C9E60F25B7368 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = shared_preferences_foundation;
			target = AB5EE685B22D01885ADD930538E8DD3C /* shared_preferences_foundation */;
			targetProxy = CA8E7D61B159F53F2DEB3B5C8FF68641 /* PBXContainerItemProxy */;
		};
		34317285D3F25FECA493AD7B4E3ADB59 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 5D285C64AE27E6B47E75D926E590F2E7 /* PBXContainerItemProxy */;
		};
		4920522DACA5D54200DC139E94115413 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "sqflite_darwin-sqflite_darwin_privacy";
			target = 9F5CAB8B25787EBAAFCC237AEDC74E2D /* sqflite_darwin-sqflite_darwin_privacy */;
			targetProxy = 3218DCC14D938F8AAE1A60D991B7C92D /* PBXContainerItemProxy */;
		};
		4D8924CF38FCCC1BEECC13CCE46DB87C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = sqflite_darwin;
			target = 6178324C15B5DCE31C127429F0C8EE8F /* sqflite_darwin */;
			targetProxy = C1D210459AF3FA427EB447D44A70CBBE /* PBXContainerItemProxy */;
		};
		51315E034258410B06A8EF97E9D7E39C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "Pods-Runner";
			target = 8B74B458B450D74B75744B87BD747314 /* Pods-Runner */;
			targetProxy = 6AE00A2210A632358B754C37392B212A /* PBXContainerItemProxy */;
		};
		5605355258CD2752604ADA0709959634 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = MTBBarcodeScanner;
			target = D83CD42EA57F97A225D24BDC7BD6B6F6 /* MTBBarcodeScanner */;
			targetProxy = C7361595CBB6FCACB2AA2979BB22671C /* PBXContainerItemProxy */;
		};
		7881C53FE7D05857C801D82166FE8A49 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "shared_preferences_foundation-shared_preferences_foundation_privacy";
			target = B6AF8B7CEAF6321719ABBC7E770624DA /* shared_preferences_foundation-shared_preferences_foundation_privacy */;
			targetProxy = C435D2D5C500FC38FD689501505D5A19 /* PBXContainerItemProxy */;
		};
		7F948BD84F0BAAA031EA69E66D87FB45 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = url_launcher_ios;
			target = DF45E85925DF410BE416B32171F59C1F /* url_launcher_ios */;
			targetProxy = A5BA2FAF1B81ADBCC2FF35B0AEE8C554 /* PBXContainerItemProxy */;
		};
		A0A1460734751F53474A1590228C97CD /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 0EDAE9521DD96EDF69198E3D7556FE07 /* PBXContainerItemProxy */;
		};
		A700013D053E0882799EE9EA2D0C22E0 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "url_launcher_ios-url_launcher_ios_privacy";
			target = 43BE97C40504244259CF3C1D21E7EDB5 /* url_launcher_ios-url_launcher_ios_privacy */;
			targetProxy = D9F8EAAE6A33DF8D10A3C2A092BE218D /* PBXContainerItemProxy */;
		};
		B9ADC0FDAC089303DE096CAB6AF77A80 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "permission_handler_apple-permission_handler_apple_privacy";
			target = 517E8BED8B0E6D6FD078BD19B0A13893 /* permission_handler_apple-permission_handler_apple_privacy */;
			targetProxy = 6F2B7FCFC14967D07582D3ABF854311E /* PBXContainerItemProxy */;
		};
		BE8E5C8EAA85434CA7DE0143B5B0B1FE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = qr_code_scanner;
			target = E864A9DA72EED9027036A46ABD1822BC /* qr_code_scanner */;
			targetProxy = 82F856DC476EA41AFFAF0A55E156C09E /* PBXContainerItemProxy */;
		};
		C574CFAD276533AE3F33976FB625A180 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = MTBBarcodeScanner;
			target = D83CD42EA57F97A225D24BDC7BD6B6F6 /* MTBBarcodeScanner */;
			targetProxy = 6416F4756398A1CBF01E39175C8BB920 /* PBXContainerItemProxy */;
		};
		F207B0D72E77210AF01B0156D32F3B92 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = DF636A2E8507C8825D1D8DB093F0F8D7 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		11099184FBFDE376BB34AC1E682C4872 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5161BC9F31CAAFF6F9F96AE24883E03F /* permission_handler_apple.debug.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = permission_handler_apple;
				PRODUCT_NAME = permission_handler_apple;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		2599B6AA44EF5DF08112B8E6B7DD7EB0 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 70594C6B00220674F02DA361FE319B72 /* permission_handler_apple.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/permission_handler_apple";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = permission_handler_apple;
				INFOPLIST_FILE = "Target Support Files/permission_handler_apple/ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = permission_handler_apple_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		28E7871BB8F4505A2762ACB51A210FF3 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 65FFD225084FCE50ADEFD7A3E8A4D984 /* MTBBarcodeScanner.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/MTBBarcodeScanner/MTBBarcodeScanner-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/MTBBarcodeScanner/MTBBarcodeScanner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/MTBBarcodeScanner/MTBBarcodeScanner.modulemap";
				PRODUCT_MODULE_NAME = MTBBarcodeScanner;
				PRODUCT_NAME = MTBBarcodeScanner;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		2A6338450EEC8C3D250F32D9E53FEFDD /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 70594C6B00220674F02DA361FE319B72 /* permission_handler_apple.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/permission_handler_apple";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = permission_handler_apple;
				INFOPLIST_FILE = "Target Support Files/permission_handler_apple/ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = permission_handler_apple_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		2B9E26EAE2CD392AD762421F663075A1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		2E4608C055BE4F4E675FECAEBC4CC2A4 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1ADB61306F50F60E65BED44878011822 /* Pods-RunnerTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		352238F70108F49434B51CFD9D646B3B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4CC19314F47EEC3204D1864FB60777CA /* shared_preferences_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = shared_preferences_foundation;
				PRODUCT_NAME = shared_preferences_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		3CD1AF7F3EB5E4F4EDE309AE4CFC8F28 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 611A55CFD7BCEFE72434A7C656553F84 /* url_launcher_ios.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/url_launcher_ios/url_launcher_ios-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/url_launcher_ios/url_launcher_ios-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/url_launcher_ios/url_launcher_ios.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = url_launcher_ios;
				PRODUCT_NAME = url_launcher_ios;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		3DD953B17D38F68349BCF4C229F543B6 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C3E607D4244AD6D9ED532E69A6DA10DD /* MTBBarcodeScanner.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/MTBBarcodeScanner/MTBBarcodeScanner-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/MTBBarcodeScanner/MTBBarcodeScanner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/MTBBarcodeScanner/MTBBarcodeScanner.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_MODULE_NAME = MTBBarcodeScanner;
				PRODUCT_NAME = MTBBarcodeScanner;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		46729596D3E861171179E26D7468B25C /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FCF84F8C05EE19EC2A6A0514C82DAB44 /* shared_preferences_foundation.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = shared_preferences_foundation;
				PRODUCT_NAME = shared_preferences_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		46E8F71496B02736FA9E05F30B268374 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F8547457089967DAC30C3130D4EDF7D1 /* Pods-Runner.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		5AF1AC88E5F655C93D1E3B5012F6FDC8 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_PROFILE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Profile;
		};
		5BEBDA7D110E5AC7717D269BA986DEDF /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4CC19314F47EEC3204D1864FB60777CA /* shared_preferences_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = shared_preferences_foundation;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = shared_preferences_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		612E06A89D9C1A5E6561967D437559F7 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B2AB91A1647649E2E356BA92D2B413E3 /* url_launcher_ios.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/url_launcher_ios";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = url_launcher_ios;
				INFOPLIST_FILE = "Target Support Files/url_launcher_ios/ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = url_launcher_ios_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		63FAF33E1C55B71A5F5A8B3CC8749F99 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		6D92E79F97BBCFD96CEE5C38A6C21349 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B2AB91A1647649E2E356BA92D2B413E3 /* url_launcher_ios.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/url_launcher_ios/url_launcher_ios-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/url_launcher_ios/url_launcher_ios-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/url_launcher_ios/url_launcher_ios.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = url_launcher_ios;
				PRODUCT_NAME = url_launcher_ios;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		70F1E3D053D812E3D7E8E74934CC9053 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AAA81655226C06A382C8295F02845641 /* Flutter.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				ONLY_ACTIVE_ARCH = NO;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		723189148229824216DB3B5536FF5DC9 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B2AB91A1647649E2E356BA92D2B413E3 /* url_launcher_ios.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/url_launcher_ios/url_launcher_ios-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/url_launcher_ios/url_launcher_ios-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/url_launcher_ios/url_launcher_ios.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = url_launcher_ios;
				PRODUCT_NAME = url_launcher_ios;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		768CE43E68DF2B5EF0C94D45F10B98DF /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4CC19314F47EEC3204D1864FB60777CA /* shared_preferences_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = shared_preferences_foundation;
				PRODUCT_NAME = shared_preferences_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		769638E58BAA61D9D0AD18A6BEC8F5E6 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8D6B0B7D1C0E40984EC17A71B2FE5D63 /* sqflite_darwin.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/sqflite_darwin";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = sqflite_darwin;
				INFOPLIST_FILE = "Target Support Files/sqflite_darwin/ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = sqflite_darwin_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		76E053E47C7357CFF4EA96DF8337031F /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 92900F972FE6ADAD91A529A224B81FBE /* Flutter.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		7BD1AB4DE0EECC079966841AD955E2F3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 70594C6B00220674F02DA361FE319B72 /* permission_handler_apple.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = permission_handler_apple;
				PRODUCT_NAME = permission_handler_apple;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		81E6D9FE80E955CE810EFACB3120CA97 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9C96F923D257ECF3D5FA15D0E8B013DA /* qr_code_scanner.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/qr_code_scanner/qr_code_scanner-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/qr_code_scanner/qr_code_scanner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/qr_code_scanner/qr_code_scanner.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = qr_code_scanner;
				PRODUCT_NAME = qr_code_scanner;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		8225955A0799FDB41C8D12909E935E0D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8D6B0B7D1C0E40984EC17A71B2FE5D63 /* sqflite_darwin.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/sqflite_darwin";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = sqflite_darwin;
				INFOPLIST_FILE = "Target Support Files/sqflite_darwin/ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = sqflite_darwin_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		857BB3D5EB9F8B8635F38EE59B6FCA5D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DD8C8ACC853AF1B657D17AEC50E540A9 /* Pods-RunnerTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		86C5BBE7CBBA66350B4E55FDF50CE26D /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1FC96341216BBB5BBE3744FB1F35DEEE /* Pods-RunnerTests.profile.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		921E75BDAEEA3C04FB567462A1292EC6 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 65FFD225084FCE50ADEFD7A3E8A4D984 /* MTBBarcodeScanner.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/MTBBarcodeScanner/MTBBarcodeScanner-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/MTBBarcodeScanner/MTBBarcodeScanner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/MTBBarcodeScanner/MTBBarcodeScanner.modulemap";
				PRODUCT_MODULE_NAME = MTBBarcodeScanner;
				PRODUCT_NAME = MTBBarcodeScanner;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		950C6A070E08ED4E061FD075C21E8879 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 611A55CFD7BCEFE72434A7C656553F84 /* url_launcher_ios.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/url_launcher_ios";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = url_launcher_ios;
				INFOPLIST_FILE = "Target Support Files/url_launcher_ios/ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = url_launcher_ios_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		98700F49EF6BB5FED287C5A443923199 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 49A077A1FB8FEFC484B8314B77DC2D56 /* sqflite_darwin.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = sqflite_darwin;
				PRODUCT_NAME = sqflite_darwin;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		A34DD29DD244A57FEAC8B42B11EEEEF0 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 49A077A1FB8FEFC484B8314B77DC2D56 /* sqflite_darwin.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/sqflite_darwin";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = sqflite_darwin;
				INFOPLIST_FILE = "Target Support Files/sqflite_darwin/ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = sqflite_darwin_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		A8013ACCC7D77B433C87FCBEC40694B0 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8D6B0B7D1C0E40984EC17A71B2FE5D63 /* sqflite_darwin.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = sqflite_darwin;
				PRODUCT_NAME = sqflite_darwin;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		A8D733E237CA009CBE98F1A449FDBDC3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4CC19314F47EEC3204D1864FB60777CA /* shared_preferences_foundation.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = shared_preferences_foundation;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = shared_preferences_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		B7C52D7E239E2FB4719C2AFBA0F57B61 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B2AB91A1647649E2E356BA92D2B413E3 /* url_launcher_ios.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/url_launcher_ios";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = url_launcher_ios;
				INFOPLIST_FILE = "Target Support Files/url_launcher_ios/ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = url_launcher_ios_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		BAD3A688C3B29EA7651D2E17CF1A9044 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0EA5EEAF9BE9E00E7E7249E412BEDCAC /* qr_code_scanner.debug.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/qr_code_scanner/qr_code_scanner-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/qr_code_scanner/qr_code_scanner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/qr_code_scanner/qr_code_scanner.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = qr_code_scanner;
				PRODUCT_NAME = qr_code_scanner;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		C404B15F29F49A60D67810DBD634D0C7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5161BC9F31CAAFF6F9F96AE24883E03F /* permission_handler_apple.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/permission_handler_apple";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = permission_handler_apple;
				INFOPLIST_FILE = "Target Support Files/permission_handler_apple/ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = permission_handler_apple_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		D09BFD3D35F6C946D9FAE547EC85B192 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 70594C6B00220674F02DA361FE319B72 /* permission_handler_apple.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = permission_handler_apple;
				PRODUCT_NAME = permission_handler_apple;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		D5C01712D3D0A433B73F14E09D9C983A /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2B3AF9D90D4B088422687FFF4641CBC3 /* Pods-Runner.profile.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		DAABDD729997FD45A2BA0AE02E3B31A3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2F0908B8A5026151E2800777E4B17F20 /* Pods-Runner.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		E87564B8C174EABA04A6DB389005F125 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9C96F923D257ECF3D5FA15D0E8B013DA /* qr_code_scanner.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/qr_code_scanner/qr_code_scanner-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/qr_code_scanner/qr_code_scanner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/qr_code_scanner/qr_code_scanner.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = qr_code_scanner;
				PRODUCT_NAME = qr_code_scanner;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		F20C9EB39AC6CF00828BDCC10294E6A8 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8D6B0B7D1C0E40984EC17A71B2FE5D63 /* sqflite_darwin.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = sqflite_darwin;
				PRODUCT_NAME = sqflite_darwin;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		FB4B781D3E4B2E85C6448C7B77E40A53 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 92900F972FE6ADAD91A529A224B81FBE /* Flutter.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		FBA2A30FD64804A7C885E6F06BEBDDA4 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FCF84F8C05EE19EC2A6A0514C82DAB44 /* shared_preferences_foundation.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = shared_preferences_foundation;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = shared_preferences_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2111E2728A33B30CCF9E68F74D020A6C /* Build configuration list for PBXNativeTarget "shared_preferences_foundation-shared_preferences_foundation_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FBA2A30FD64804A7C885E6F06BEBDDA4 /* Debug */,
				5BEBDA7D110E5AC7717D269BA986DEDF /* Profile */,
				A8D733E237CA009CBE98F1A449FDBDC3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		38D3ADF0652CE382589F1F3CE1EEE84D /* Build configuration list for PBXNativeTarget "MTBBarcodeScanner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3DD953B17D38F68349BCF4C229F543B6 /* Debug */,
				28E7871BB8F4505A2762ACB51A210FF3 /* Profile */,
				921E75BDAEEA3C04FB567462A1292EC6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2B9E26EAE2CD392AD762421F663075A1 /* Debug */,
				5AF1AC88E5F655C93D1E3B5012F6FDC8 /* Profile */,
				63FAF33E1C55B71A5F5A8B3CC8749F99 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		51E44378D0EE45FC26653A27A9669C35 /* Build configuration list for PBXNativeTarget "shared_preferences_foundation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				46729596D3E861171179E26D7468B25C /* Debug */,
				768CE43E68DF2B5EF0C94D45F10B98DF /* Profile */,
				352238F70108F49434B51CFD9D646B3B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		77A2AFCE4E6AE140D9C60A032C6D8B33 /* Build configuration list for PBXNativeTarget "url_launcher_ios-url_launcher_ios_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				950C6A070E08ED4E061FD075C21E8879 /* Debug */,
				B7C52D7E239E2FB4719C2AFBA0F57B61 /* Profile */,
				612E06A89D9C1A5E6561967D437559F7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		84AAE47AFE07B5E560E9FE58ABDEEEDA /* Build configuration list for PBXNativeTarget "sqflite_darwin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				98700F49EF6BB5FED287C5A443923199 /* Debug */,
				F20C9EB39AC6CF00828BDCC10294E6A8 /* Profile */,
				A8013ACCC7D77B433C87FCBEC40694B0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		89EFA95D7A42D94E2576D20D9240C679 /* Build configuration list for PBXNativeTarget "permission_handler_apple" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				11099184FBFDE376BB34AC1E682C4872 /* Debug */,
				D09BFD3D35F6C946D9FAE547EC85B192 /* Profile */,
				7BD1AB4DE0EECC079966841AD955E2F3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8FCD22AFD0157EBB2B49F3BC9CE14BC5 /* Build configuration list for PBXNativeTarget "permission_handler_apple-permission_handler_apple_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C404B15F29F49A60D67810DBD634D0C7 /* Debug */,
				2A6338450EEC8C3D250F32D9E53FEFDD /* Profile */,
				2599B6AA44EF5DF08112B8E6B7DD7EB0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		94078A06C17E946ADC1F2C06726219E5 /* Build configuration list for PBXAggregateTarget "Flutter" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				70F1E3D053D812E3D7E8E74934CC9053 /* Debug */,
				76E053E47C7357CFF4EA96DF8337031F /* Profile */,
				FB4B781D3E4B2E85C6448C7B77E40A53 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A510A753FD3AF1785BCB45107877CB90 /* Build configuration list for PBXNativeTarget "url_launcher_ios" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3CD1AF7F3EB5E4F4EDE309AE4CFC8F28 /* Debug */,
				6D92E79F97BBCFD96CEE5C38A6C21349 /* Profile */,
				723189148229824216DB3B5536FF5DC9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B86BDF942AE09555F4C85B37CFEAE949 /* Build configuration list for PBXNativeTarget "qr_code_scanner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BAD3A688C3B29EA7651D2E17CF1A9044 /* Debug */,
				81E6D9FE80E955CE810EFACB3120CA97 /* Profile */,
				E87564B8C174EABA04A6DB389005F125 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CD157E8A1CBECC1336B2D185DFC72DEA /* Build configuration list for PBXNativeTarget "sqflite_darwin-sqflite_darwin_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A34DD29DD244A57FEAC8B42B11EEEEF0 /* Debug */,
				769638E58BAA61D9D0AD18A6BEC8F5E6 /* Profile */,
				8225955A0799FDB41C8D12909E935E0D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D52099AA1537D0F3745166E1889F6CA3 /* Build configuration list for PBXNativeTarget "Pods-RunnerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				857BB3D5EB9F8B8635F38EE59B6FCA5D /* Debug */,
				86C5BBE7CBBA66350B4E55FDF50CE26D /* Profile */,
				2E4608C055BE4F4E675FECAEBC4CC2A4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F3B4C638A1D12D9740DBFA0984314635 /* Build configuration list for PBXNativeTarget "Pods-Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DAABDD729997FD45A2BA0AE02E3B31A3 /* Debug */,
				D5C01712D3D0A433B73F14E09D9C983A /* Profile */,
				46E8F71496B02736FA9E05F30B268374 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
